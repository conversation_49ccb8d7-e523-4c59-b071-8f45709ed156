"""
Comprehensive test suite for the enhanced Advanced Coding Agent
Tests all new features including smart decision making, token optimization,
performance improvements, and GUI interface.
"""

import unittest
import sys
import os
import time
import tempfile
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent import (
    AdvancedCodingAgent, 
    IntelligentDecisionEngine, 
    TokenOptimizer, 
    SmartOrchestrator,
    DecisionNode,
    LearningMemory,
    TokenUsage,
    AgentContext
)

class TestSmartDecisionMaking(unittest.TestCase):
    """Test smart decision making and reasoning capabilities"""
    
    def setUp(self):
        self.context = AgentContext()
        self.decision_engine = IntelligentDecisionEngine(self.context)
    
    def test_decision_creation(self):
        """Test decision node creation"""
        decision = DecisionNode(
            question="What should I do?",
            options=["Option A", "Option B"],
            chosen_option="Option A",
            reasoning="Option A is better",
            confidence=0.8
        )
        
        self.assertEqual(decision.question, "What should I do?")
        self.assertEqual(decision.chosen_option, "Option A")
        self.assertEqual(decision.confidence, 0.8)
        self.assertIsNotNone(decision.decision_id)
    
    def test_learning_memory(self):
        """Test learning memory functionality"""
        learning = LearningMemory(
            context="Test context",
            action_taken="Test action",
            outcome="Success",
            success_score=0.9
        )
        
        self.assertEqual(learning.context, "Test context")
        self.assertEqual(learning.success_score, 0.9)
        self.assertIsNotNone(learning.experience_id)
    
    @patch('agent.llm')
    def test_decision_making_process(self, mock_llm):
        """Test the decision making process"""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = """
        CHOICE: Option A
        REASONING: This is the best choice because it's optimal
        CONFIDENCE: 0.8
        RISKS: Minimal risks involved
        """
        mock_llm.invoke.return_value = mock_response
        
        decision = self.decision_engine.make_decision(
            "Which option should I choose?",
            ["Option A", "Option B"],
            "Test context"
        )
        
        self.assertEqual(decision.chosen_option, "Option A")
        self.assertGreater(decision.confidence, 0.0)
        self.assertIn("optimal", decision.reasoning.lower())
    
    def test_similar_decision_finding(self):
        """Test finding similar previous decisions"""
        # Add a decision to history
        past_decision = DecisionNode(
            question="Should I use Python or JavaScript?",
            chosen_option="Python",
            reasoning="Python is better for this task"
        )
        self.context.decision_history.append(past_decision)
        
        # Find similar decision
        similar = self.decision_engine._find_similar_decision("Which language Python or JS?")
        
        self.assertIsNotNone(similar)
        self.assertEqual(similar.chosen_option, "Python")

class TestTokenOptimization(unittest.TestCase):
    """Test token usage optimization features"""
    
    def setUp(self):
        self.context = AgentContext()
        self.token_optimizer = TokenOptimizer(self.context)
    
    def test_token_usage_tracking(self):
        """Test token usage tracking"""
        initial_tokens = self.context.token_usage.request_tokens
        
        # Simulate token usage
        self.token_optimizer.optimize_prompt("Test prompt", "Test context")
        
        self.assertGreater(self.context.token_usage.request_tokens, initial_tokens)
    
    def test_prompt_compression(self):
        """Test prompt compression functionality"""
        long_prompt = "I would like you to please provide me with a solution that basically does what I need"
        compressed = self.token_optimizer._compress_prompt(long_prompt)
        
        self.assertLess(len(compressed), len(long_prompt))
        self.assertNotIn("basically", compressed)
        self.assertNotIn("I would like you to", compressed)
    
    def test_redundancy_removal(self):
        """Test redundancy removal"""
        redundant_text = "This is a test. This is a test. This is different."
        cleaned = self.token_optimizer._remove_redundancy(redundant_text)
        
        # Should have only one instance of "This is a test"
        self.assertEqual(cleaned.count("This is a test"), 1)
        self.assertIn("This is different", cleaned)
    
    def test_response_caching(self):
        """Test response caching"""
        prompt = "Test prompt"
        response = "Test response"
        
        # Cache response
        self.token_optimizer.cache_response(prompt, response)
        
        # Check if cached
        self.assertGreater(len(self.token_optimizer.response_cache), 0)
    
    def test_token_limit_checking(self):
        """Test token limit checking"""
        # Should be under limit initially
        self.assertTrue(self.token_optimizer.check_token_limit())
        
        # Simulate high token usage
        self.context.token_usage.session_tokens = 90000
        self.assertFalse(self.token_optimizer.check_token_limit())
    
    def test_token_usage_report(self):
        """Test token usage report generation"""
        self.context.token_usage.session_tokens = 1000
        self.context.token_usage.request_tokens = 500
        self.context.token_usage.response_tokens = 300
        
        report = self.token_optimizer.get_token_usage_report()
        
        self.assertIn("Session Tokens: 1,000", report)
        self.assertIn("Request Tokens: 500", report)
        self.assertIn("Response Tokens: 300", report)
        self.assertIn("Total Used: 1,800", report)

class TestPerformanceEnhancements(unittest.TestCase):
    """Test performance and efficiency improvements"""
    
    def setUp(self):
        self.agent = AdvancedCodingAgent()
    
    def test_performance_monitoring(self):
        """Test performance monitoring functionality"""
        # Monitor a test operation
        self.agent.monitor_performance("test_operation", 2.5)
        
        # Check if recorded
        self.assertGreater(len(self.agent.performance_metrics["command_execution_times"]), 0)
        
        # Check recorded data
        last_metric = self.agent.performance_metrics["command_execution_times"][-1]
        self.assertEqual(last_metric["operation"], "test_operation")
        self.assertEqual(last_metric["time"], 2.5)
    
    def test_auto_optimization_trigger(self):
        """Test automatic optimization when performance degrades"""
        # Simulate slow operations
        for i in range(10):
            self.agent.monitor_performance(f"slow_operation_{i}", 6.0)
        
        # Should trigger auto-optimization
        # Check if caches were cleared (they should be empty after optimization)
        self.assertEqual(len(self.agent.cache), 0)
    
    def test_performance_report_generation(self):
        """Test performance report generation"""
        # Add some performance data
        self.agent.monitor_performance("test_op_1", 1.5)
        self.agent.monitor_performance("test_op_2", 2.0)
        self.agent.monitor_performance("test_op_3", 1.0)
        
        report = self.agent.get_performance_report()
        
        self.assertIn("Average execution time:", report)
        self.assertIn("Fastest operation:", report)
        self.assertIn("Slowest operation:", report)
        self.assertIn("Total operations tracked:", report)
    
    def test_error_loop_prevention(self):
        """Test prevention of infinite error loops"""
        # This would be tested in integration, but we can test the logic
        agent = self.agent
        
        # Simulate recurring errors
        if not hasattr(agent, '_recent_errors'):
            agent._recent_errors = []
        
        error_signature = "TestError: Test error message"
        
        # Add same error multiple times
        for _ in range(3):
            agent._recent_errors.append(error_signature)
        
        # Check if error would be caught
        self.assertEqual(agent._recent_errors.count(error_signature), 3)

class TestSmartOrchestration(unittest.TestCase):
    """Test smart orchestration capabilities"""
    
    def setUp(self):
        self.context = AgentContext()
        self.orchestrator = SmartOrchestrator(self.context)
    
    def test_intent_analysis(self):
        """Test intent analysis functionality"""
        # Test creation intent
        result = self.orchestrator.analyze_intent("Create a new React application with authentication")
        
        self.assertEqual(result["primary_intent"], "create_project")
        self.assertIn("create_project", result["all_intents"])
        self.assertTrue(result["requires_planning"])
    
    def test_multiple_intent_detection(self):
        """Test detection of multiple intents"""
        result = self.orchestrator.analyze_intent("Create and test a new Python function")
        
        self.assertGreater(len(result["all_intents"]), 1)
        self.assertIn("confidence_scores", result)
    
    def test_execution_plan_creation(self):
        """Test execution plan creation"""
        intent_analysis = {
            "primary_intent": "create_project",
            "requires_planning": True
        }
        
        plan = self.orchestrator.create_execution_plan(intent_analysis, "Create a web app")
        
        self.assertGreater(len(plan), 0)
        self.assertEqual(plan[0].status, "pending")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        self.agent = AdvancedCodingAgent()
    
    def test_agent_initialization(self):
        """Test that agent initializes properly with all enhancements"""
        self.assertIsNotNone(self.agent.context)
        self.assertIsNotNone(self.agent.orchestrator)
        self.assertIsNotNone(self.agent.token_optimizer)
        self.assertIsNotNone(self.agent.tui)
        
        # Check that decision engine is initialized
        self.assertIsNotNone(self.agent.orchestrator.decision_engine)
    
    def test_context_enhancements(self):
        """Test that context has all new features"""
        context = self.agent.context
        
        self.assertIsNotNone(context.decision_history)
        self.assertIsNotNone(context.learning_memory)
        self.assertIsNotNone(context.token_usage)
        
        # Check session stats include new metrics
        self.assertIn("decisions_made", context.session_stats)
        self.assertIn("learning_experiences", context.session_stats)
    
    def test_tool_creation(self):
        """Test that all tools are created successfully"""
        tools = self.agent.create_tools()
        
        self.assertGreater(len(tools), 50)  # Should have many tools
        
        # Check for some key tools
        tool_names = [tool.name for tool in tools]
        self.assertIn("run_command", tool_names)
        self.assertIn("write_file", tool_names)
        self.assertIn("generate_code", tool_names)
    
    @patch('agent.llm')
    def test_optimized_llm_usage(self, mock_llm):
        """Test that LLM usage is optimized"""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = "Test response"
        mock_llm.invoke.return_value = mock_response
        
        # Test prompt optimization
        original_prompt = "This is a test prompt that needs optimization"
        optimized = self.agent.token_optimizer.optimize_prompt(original_prompt)
        
        # Should be optimized (different from original)
        self.assertIsNotNone(optimized)

def run_gui_test():
    """Test GUI functionality if available"""
    try:
        from gui_interface import ProfessionalGUI, ModernTheme
        
        print("🖥️  Testing GUI components...")
        
        # Test theme
        theme = ModernTheme()
        assert hasattr(theme, 'BG_PRIMARY')
        assert hasattr(theme, 'FG_PRIMARY')
        print("✅ Theme configuration: OK")
        
        # Test GUI initialization (without actually showing it)
        agent = AdvancedCodingAgent()
        
        # Mock tkinter to avoid GUI display during testing
        with patch('tkinter.Tk'):
            gui = ProfessionalGUI(agent)
            assert gui.agent == agent
            assert gui.theme is not None
            print("✅ GUI initialization: OK")
        
        print("✅ GUI tests completed successfully")
        
    except ImportError:
        print("⚠️  GUI dependencies not available - skipping GUI tests")
    except Exception as e:
        print(f"❌ GUI test failed: {e}")

def main():
    """Run all tests"""
    print("🧪 Starting comprehensive test suite for Enhanced Coding Agent...")
    print("=" * 70)
    
    # Run unit tests
    test_suite = unittest.TestLoader().loadTestsFromModule(sys.modules[__name__])
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    print("\n" + "=" * 70)
    
    # Run GUI tests
    run_gui_test()
    
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n❌ Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n🎯 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 Excellent! All major enhancements are working correctly.")
    elif success_rate >= 75:
        print("✅ Good! Most enhancements are working with minor issues.")
    else:
        print("⚠️  Some enhancements need attention.")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
