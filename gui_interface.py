"""
Professional GUI Interface for Advanced Coding Agent
Inspired by OpenCode AI, <PERSON>'s interface, Gemini CLI, and Codex CLI
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import json
import os
from datetime import datetime
from typing import Dict, List, Any
import re

class ModernTheme:
    """Modern dark theme inspired by VS Code and Claude"""
    
    # Dark theme colors
    BG_PRIMARY = "#1e1e1e"      # Main background
    BG_SECONDARY = "#252526"    # Secondary background
    BG_TERTIARY = "#2d2d30"     # Tertiary background
    
    # Text colors
    FG_PRIMARY = "#cccccc"      # Primary text
    FG_SECONDARY = "#969696"    # Secondary text
    FG_ACCENT = "#007acc"       # Accent color (blue)
    FG_SUCCESS = "#4ec9b0"      # Success color (teal)
    FG_WARNING = "#dcdcaa"      # Warning color (yellow)
    FG_ERROR = "#f44747"        # Error color (red)
    
    # Syntax highlighting colors
    SYNTAX_KEYWORD = "#569cd6"   # Keywords (blue)
    SYNTAX_STRING = "#ce9178"    # Strings (orange)
    SYNTAX_COMMENT = "#6a9955"   # Comments (green)
    SYNTAX_NUMBER = "#b5cea8"    # Numbers (light green)
    SYNTAX_FUNCTION = "#dcdcaa"  # Functions (yellow)

class ProfessionalGUI:
    """Professional GUI interface for the coding agent"""
    
    def __init__(self, agent):
        self.agent = agent
        self.theme = ModernTheme()
        self.root = tk.Tk()
        self.message_queue = queue.Queue()
        self.setup_window()
        self.create_widgets()
        self.setup_syntax_highlighting()
        self.setup_key_bindings()
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("Claude Code - Elite Coding Agent")
        self.root.geometry("1400x900")
        self.root.configure(bg=self.theme.BG_PRIMARY)
        self.root.minsize(1000, 600)
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """Create and layout all GUI widgets"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.theme.BG_PRIMARY)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header frame
        self.create_header(main_frame)
        
        # Main content area with paned window
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Left panel (chat/interaction)
        self.create_chat_panel(paned_window)
        
        # Right panel (code editor and tools)
        self.create_tools_panel(paned_window)
        
        # Status bar
        self.create_status_bar(main_frame)
        
    def create_header(self, parent):
        """Create header with title and controls"""
        header_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=60)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="claude",
            font=("Segoe UI", 24, "bold"),
            fg=self.theme.FG_ACCENT,
            bg=self.theme.BG_SECONDARY
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="Elite Coding Agent v3.0 - Professional Interface",
            font=("Segoe UI", 10),
            fg=self.theme.FG_SECONDARY,
            bg=self.theme.BG_SECONDARY
        )
        subtitle_label.pack(side=tk.LEFT, padx=(0, 20), pady=15)
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg=self.theme.BG_SECONDARY)
        controls_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # Settings button
        settings_btn = tk.Button(
            controls_frame,
            text="⚙️ Settings",
            font=("Segoe UI", 9),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            padx=15,
            pady=5,
            command=self.show_settings
        )
        settings_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Status button
        status_btn = tk.Button(
            controls_frame,
            text="📊 Status",
            font=("Segoe UI", 9),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            padx=15,
            pady=5,
            command=self.show_status
        )
        status_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_chat_panel(self, parent):
        """Create chat/interaction panel"""
        chat_frame = tk.Frame(parent, bg=self.theme.BG_PRIMARY)
        parent.add(chat_frame, weight=2)
        
        # Chat history
        chat_label = tk.Label(
            chat_frame,
            text="Conversation",
            font=("Segoe UI", 12, "bold"),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_PRIMARY
        )
        chat_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Chat display area
        self.chat_display = scrolledtext.ScrolledText(
            chat_frame,
            font=("Consolas", 10),
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.FG_PRIMARY,
            insertbackground=self.theme.FG_PRIMARY,
            selectbackground=self.theme.FG_ACCENT,
            relief=tk.FLAT,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Input area
        input_frame = tk.Frame(chat_frame, bg=self.theme.BG_PRIMARY)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input label
        input_label = tk.Label(
            input_frame,
            text="Your request:",
            font=("Segoe UI", 10),
            fg=self.theme.FG_SECONDARY,
            bg=self.theme.BG_PRIMARY
        )
        input_label.pack(anchor=tk.W, pady=(0, 5))
        
        # Input text area
        self.input_text = tk.Text(
            input_frame,
            font=("Consolas", 10),
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.FG_PRIMARY,
            insertbackground=self.theme.FG_PRIMARY,
            selectbackground=self.theme.FG_ACCENT,
            relief=tk.FLAT,
            height=3,
            wrap=tk.WORD
        )
        self.input_text.pack(fill=tk.X, pady=(0, 10))
        
        # Button frame
        button_frame = tk.Frame(input_frame, bg=self.theme.BG_PRIMARY)
        button_frame.pack(fill=tk.X)
        
        # Send button
        self.send_button = tk.Button(
            button_frame,
            text="Send",
            font=("Segoe UI", 10, "bold"),
            fg="white",
            bg=self.theme.FG_ACCENT,
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=self.send_message
        )
        self.send_button.pack(side=tk.RIGHT)
        
        # Clear button
        clear_button = tk.Button(
            button_frame,
            text="Clear",
            font=("Segoe UI", 10),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            padx=15,
            pady=8,
            command=self.clear_chat
        )
        clear_button.pack(side=tk.RIGHT, padx=(0, 10))
        
    def create_tools_panel(self, parent):
        """Create tools and code editor panel"""
        tools_frame = tk.Frame(parent, bg=self.theme.BG_PRIMARY)
        parent.add(tools_frame, weight=1)
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(tools_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Code editor tab
        self.create_code_editor_tab()
        
        # File explorer tab
        self.create_file_explorer_tab()
        
        # Tools tab
        self.create_tools_tab()
        
    def create_code_editor_tab(self):
        """Create code editor tab"""
        editor_frame = tk.Frame(self.notebook, bg=self.theme.BG_PRIMARY)
        self.notebook.add(editor_frame, text="Code Editor")
        
        # Editor toolbar
        toolbar = tk.Frame(editor_frame, bg=self.theme.BG_SECONDARY, height=40)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        toolbar.pack_propagate(False)
        
        # File operations
        open_btn = tk.Button(
            toolbar,
            text="📁 Open",
            font=("Segoe UI", 9),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            padx=10,
            pady=5,
            command=self.open_file
        )
        open_btn.pack(side=tk.LEFT, padx=10, pady=5)
        
        save_btn = tk.Button(
            toolbar,
            text="💾 Save",
            font=("Segoe UI", 9),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            padx=10,
            pady=5,
            command=self.save_file
        )
        save_btn.pack(side=tk.LEFT, padx=(5, 10), pady=5)
        
        # Code editor
        self.code_editor = scrolledtext.ScrolledText(
            editor_frame,
            font=("Consolas", 11),
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.FG_PRIMARY,
            insertbackground=self.theme.FG_PRIMARY,
            selectbackground=self.theme.FG_ACCENT,
            relief=tk.FLAT,
            wrap=tk.NONE,
            tabs=("1c", "2c", "3c", "4c")  # Tab stops for indentation
        )
        self.code_editor.pack(fill=tk.BOTH, expand=True)
        
    def create_file_explorer_tab(self):
        """Create file explorer tab"""
        explorer_frame = tk.Frame(self.notebook, bg=self.theme.BG_PRIMARY)
        self.notebook.add(explorer_frame, text="Files")
        
        # File tree
        self.file_tree = ttk.Treeview(explorer_frame)
        self.file_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Populate with current directory
        self.refresh_file_tree()
        
    def create_tools_tab(self):
        """Create tools and utilities tab"""
        tools_frame = tk.Frame(self.notebook, bg=self.theme.BG_PRIMARY)
        self.notebook.add(tools_frame, text="Tools")
        
        # Quick actions
        actions_label = tk.Label(
            tools_frame,
            text="Quick Actions",
            font=("Segoe UI", 12, "bold"),
            fg=self.theme.FG_PRIMARY,
            bg=self.theme.BG_PRIMARY
        )
        actions_label.pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        # Action buttons
        actions = [
            ("🔍 Analyze Code", self.analyze_code),
            ("🧪 Run Tests", self.run_tests),
            ("🔧 Fix Errors", self.fix_errors),
            ("📊 Performance Report", self.show_performance),
            ("🎯 Token Usage", self.show_token_usage)
        ]
        
        for text, command in actions:
            btn = tk.Button(
                tools_frame,
                text=text,
                font=("Segoe UI", 10),
                fg=self.theme.FG_PRIMARY,
                bg=self.theme.BG_TERTIARY,
                relief=tk.FLAT,
                padx=15,
                pady=8,
                command=command,
                anchor=tk.W
            )
            btn.pack(fill=tk.X, padx=10, pady=2)

    def create_status_bar(self, parent):
        """Create status bar"""
        self.status_bar = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=25)
        self.status_bar.pack(fill=tk.X, pady=(10, 0))
        self.status_bar.pack_propagate(False)

        # Status text
        self.status_text = tk.Label(
            self.status_bar,
            text="Ready",
            font=("Segoe UI", 9),
            fg=self.theme.FG_SECONDARY,
            bg=self.theme.BG_SECONDARY
        )
        self.status_text.pack(side=tk.LEFT, padx=10, pady=3)

        # Token usage indicator
        self.token_indicator = tk.Label(
            self.status_bar,
            text="Tokens: 0/100K",
            font=("Segoe UI", 9),
            fg=self.theme.FG_SECONDARY,
            bg=self.theme.BG_SECONDARY
        )
        self.token_indicator.pack(side=tk.RIGHT, padx=10, pady=3)

    def setup_syntax_highlighting(self):
        """Setup syntax highlighting for code editor"""
        # Configure text tags for syntax highlighting
        self.code_editor.tag_configure("keyword", foreground=self.theme.SYNTAX_KEYWORD)
        self.code_editor.tag_configure("string", foreground=self.theme.SYNTAX_STRING)
        self.code_editor.tag_configure("comment", foreground=self.theme.SYNTAX_COMMENT)
        self.code_editor.tag_configure("number", foreground=self.theme.SYNTAX_NUMBER)
        self.code_editor.tag_configure("function", foreground=self.theme.SYNTAX_FUNCTION)

        # Bind text change event for real-time highlighting
        self.code_editor.bind('<KeyRelease>', self.highlight_syntax)

    def setup_key_bindings(self):
        """Setup keyboard shortcuts"""
        self.root.bind('<Control-Return>', lambda e: self.send_message())
        self.root.bind('<Control-l>', lambda e: self.clear_chat())
        self.root.bind('<Control-o>', lambda e: self.open_file())
        self.root.bind('<Control-s>', lambda e: self.save_file())
        self.root.bind('<F5>', lambda e: self.run_tests())

    def highlight_syntax(self, event=None):
        """Apply syntax highlighting to code editor"""
        try:
            content = self.code_editor.get("1.0", tk.END)

            # Clear existing tags
            for tag in ["keyword", "string", "comment", "number", "function"]:
                self.code_editor.tag_remove(tag, "1.0", tk.END)

            # Python keywords
            keywords = [
                "def", "class", "if", "else", "elif", "for", "while", "try", "except",
                "finally", "with", "as", "import", "from", "return", "yield", "lambda",
                "and", "or", "not", "in", "is", "True", "False", "None"
            ]

            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                # Highlight keywords
                for keyword in keywords:
                    pattern = rf'\b{keyword}\b'
                    for match in re.finditer(pattern, line):
                        start = f"{line_num}.{match.start()}"
                        end = f"{line_num}.{match.end()}"
                        self.code_editor.tag_add("keyword", start, end)

                # Highlight strings
                string_patterns = [r'"[^"]*"', r"'[^']*'", r'""".*?"""', r"'''.*?'''"]
                for pattern in string_patterns:
                    for match in re.finditer(pattern, line):
                        start = f"{line_num}.{match.start()}"
                        end = f"{line_num}.{match.end()}"
                        self.code_editor.tag_add("string", start, end)

                # Highlight comments
                comment_match = re.search(r'#.*$', line)
                if comment_match:
                    start = f"{line_num}.{comment_match.start()}"
                    end = f"{line_num}.{comment_match.end()}"
                    self.code_editor.tag_add("comment", start, end)

                # Highlight numbers
                for match in re.finditer(r'\b\d+\.?\d*\b', line):
                    start = f"{line_num}.{match.start()}"
                    end = f"{line_num}.{match.end()}"
                    self.code_editor.tag_add("number", start, end)

                # Highlight function definitions
                func_match = re.search(r'def\s+(\w+)', line)
                if func_match:
                    start = f"{line_num}.{func_match.start(1)}"
                    end = f"{line_num}.{func_match.end(1)}"
                    self.code_editor.tag_add("function", start, end)

        except Exception as e:
            pass  # Ignore highlighting errors

    def send_message(self):
        """Send message to agent"""
        message = self.input_text.get("1.0", tk.END).strip()
        if not message:
            return

        # Clear input
        self.input_text.delete("1.0", tk.END)

        # Add user message to chat
        self.add_to_chat("You", message, self.theme.FG_ACCENT)

        # Update status
        self.update_status("Processing request...")

        # Disable send button
        self.send_button.config(state=tk.DISABLED)

        # Process in background thread
        threading.Thread(target=self.process_message, args=(message,), daemon=True).start()

    def process_message(self, message):
        """Process message in background thread"""
        try:
            # Create a simple context for the agent
            context_info = f"""
🔍 GUI CONTEXT:
- Interface: Professional GUI Mode
- User Request: {message}
- Current Directory: {os.getcwd()}

🎯 USER REQUEST: {message}

Please provide a clear, concise response suitable for GUI display."""

            # Use the agent's LLM directly for faster response
            from langchain.schema import HumanMessage
            response = self.agent.llm.invoke([HumanMessage(content=context_info)])

            # Add response to chat
            self.message_queue.put(("Assistant", response.content, self.theme.FG_SUCCESS))

        except Exception as e:
            self.message_queue.put(("Error", f"Failed to process request: {str(e)}", self.theme.FG_ERROR))
        finally:
            self.message_queue.put(("STATUS", "Ready", None))
            self.message_queue.put(("ENABLE_SEND", None, None))

    def add_to_chat(self, sender, message, color):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)

        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Add sender and timestamp
        self.chat_display.insert(tk.END, f"[{timestamp}] {sender}:\n", "sender")
        self.chat_display.tag_configure("sender", foreground=color, font=("Segoe UI", 10, "bold"))

        # Add message
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        self.chat_display.tag_configure("message", foreground=self.theme.FG_PRIMARY, font=("Segoe UI", 10))

        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)

    def clear_chat(self):
        """Clear chat history"""
        self.chat_display.config(state=tk.NORMAL)
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state=tk.DISABLED)

    def update_status(self, text):
        """Update status bar text"""
        self.status_text.config(text=text)

    def update_token_indicator(self):
        """Update token usage indicator"""
        if hasattr(self.agent, 'context') and hasattr(self.agent.context, 'token_usage'):
            total_tokens = (self.agent.context.token_usage.session_tokens +
                           self.agent.context.token_usage.request_tokens +
                           self.agent.context.token_usage.response_tokens)
            limit = self.agent.context.token_usage.token_limit

            self.token_indicator.config(text=f"Tokens: {total_tokens:,}/{limit:,}")

            # Change color based on usage
            usage_percent = (total_tokens / limit) * 100
            if usage_percent > 80:
                color = self.theme.FG_ERROR
            elif usage_percent > 60:
                color = self.theme.FG_WARNING
            else:
                color = self.theme.FG_SUCCESS

            self.token_indicator.config(foreground=color)

    def refresh_file_tree(self):
        """Refresh file explorer tree"""
        try:
            # Clear existing items
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)

            # Add current directory files
            current_dir = os.getcwd()
            for item in os.listdir(current_dir):
                if os.path.isfile(item):
                    self.file_tree.insert("", tk.END, text=f"📄 {item}", values=(item,))
                elif os.path.isdir(item) and not item.startswith('.'):
                    self.file_tree.insert("", tk.END, text=f"📁 {item}", values=(item,))

        except Exception as e:
            pass

    # Tool methods
    def open_file(self):
        """Open file in code editor"""
        file_path = filedialog.askopenfilename(
            title="Open File",
            filetypes=[
                ("Python files", "*.py"),
                ("JavaScript files", "*.js"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.code_editor.delete("1.0", tk.END)
                self.code_editor.insert("1.0", content)
                self.highlight_syntax()

                self.update_status(f"Opened: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to open file: {str(e)}")

    def save_file(self):
        """Save code editor content"""
        file_path = filedialog.asksaveasfilename(
            title="Save File",
            defaultextension=".py",
            filetypes=[
                ("Python files", "*.py"),
                ("JavaScript files", "*.js"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                content = self.code_editor.get("1.0", tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.update_status(f"Saved: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def analyze_code(self):
        """Analyze code in editor"""
        code = self.code_editor.get("1.0", tk.END).strip()
        if code:
            self.input_text.insert("1.0", f"Analyze this code:\n\n{code}")
            self.send_message()

    def run_tests(self):
        """Run tests"""
        self.input_text.insert("1.0", "Run tests for the current project")
        self.send_message()

    def fix_errors(self):
        """Fix errors in code"""
        code = self.code_editor.get("1.0", tk.END).strip()
        if code:
            self.input_text.insert("1.0", f"Fix errors in this code:\n\n{code}")
            self.send_message()

    def show_performance(self):
        """Show performance report"""
        if hasattr(self.agent, 'get_performance_report'):
            report = self.agent.get_performance_report()
            self.add_to_chat("System", report, self.theme.FG_WARNING)

    def show_token_usage(self):
        """Show token usage report"""
        if hasattr(self.agent, 'token_optimizer'):
            report = self.agent.token_optimizer.get_token_usage_report()
            self.add_to_chat("System", report, self.theme.FG_WARNING)

    def show_settings(self):
        """Show settings dialog"""
        messagebox.showinfo("Settings", "Settings panel coming soon!")

    def show_status(self):
        """Show detailed status"""
        if hasattr(self.agent, 'show_status_claude_style'):
            # Capture status output
            import io
            import sys

            old_stdout = sys.stdout
            sys.stdout = buffer = io.StringIO()

            try:
                self.agent.show_status_claude_style()
                status_output = buffer.getvalue()
                self.add_to_chat("System Status", status_output, self.theme.FG_SECONDARY)
            finally:
                sys.stdout = old_stdout

    def process_message_queue(self):
        """Process messages from background threads"""
        try:
            while True:
                sender, message, color = self.message_queue.get_nowait()

                if sender == "STATUS":
                    self.update_status(message)
                elif sender == "ENABLE_SEND":
                    self.send_button.config(state=tk.NORMAL)
                else:
                    self.add_to_chat(sender, message, color)

        except queue.Empty:
            pass

        # Update token indicator
        self.update_token_indicator()

        # Schedule next check
        self.root.after(100, self.process_message_queue)

    def run(self):
        """Start the GUI application"""
        # Start message queue processing
        self.process_message_queue()

        # Add welcome message
        self.add_to_chat(
            "Claude Code",
            "Welcome to Claude Code - Elite Coding Agent!\n\nI'm ready to help you with:\n• Code generation and analysis\n• Debugging and error fixing\n• Project management\n• Testing and optimization\n\nType your request below or use the tools panel on the right.",
            self.theme.FG_SUCCESS
        )

        # Start main loop
        self.root.mainloop()

def launch_gui(agent):
    """Launch the professional GUI interface"""
    gui = ProfessionalGUI(agent)
    gui.run()
