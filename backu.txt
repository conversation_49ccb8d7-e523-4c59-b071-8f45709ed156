import json
import os
import time
import subprocess
import threading
import asyncio
import concurrent.futures
import re
import ast
import shutil
import glob
import urllib.request
import urllib.parse
import tempfile
import zipfile
import tarfile
import csv
import xml.etree.ElementTree as ET
import sqlite3
import logging
import signal
import psutil
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import pickle
import difflib
import tokenize
import io
import inspect
import importlib
import sys
import platform
import uuid
import yaml
import toml

# Enhanced TUI imports
try:
    import colorama
    from colorama import Fore, Back, Style, init
    init(autoreset=True)
    COLORS_AVAILABLE = True
except ImportError:
    COLORS_AVAILABLE = False
    # Fallback color definitions
    class Fore:
        RED = YELLOW = GREEN = CYAN = BLUE = MAGENTA = WHITE = RESET = ""
    class Back:
        BLACK = RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Style:
        DIM = NORMAL = BRIGHT = RESET_ALL = ""

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.tools import Tool
from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferWindowMemory
from langchain.callbacks import StreamingStdOutCallbackHandler

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Tokyo Night Theme Colors
class TokyoNightTheme:
    """Tokyo Night color scheme for enhanced TUI"""

    # Background colors
    BG_DARK = "\033[48;2;26;27;38m" if COLORS_AVAILABLE else ""
    BG_DARKER = "\033[48;2;22;22;30m" if COLORS_AVAILABLE else ""
    BG_LIGHT = "\033[48;2;36;40;59m" if COLORS_AVAILABLE else ""

    # Foreground colors
    FG_WHITE = "\033[38;2;192;202;245m" if COLORS_AVAILABLE else ""
    FG_GRAY = "\033[38;2;116;125;175m" if COLORS_AVAILABLE else ""
    FG_DARK_GRAY = "\033[38;2;86;95;137m" if COLORS_AVAILABLE else ""

    # Accent colors
    PURPLE = "\033[38;2;187;154;247m" if COLORS_AVAILABLE else ""
    BLUE = "\033[38;2;125;207;255m" if COLORS_AVAILABLE else ""
    CYAN = "\033[38;2;125;207;255m" if COLORS_AVAILABLE else ""
    GREEN = "\033[38;2;158;206;106m" if COLORS_AVAILABLE else ""
    YELLOW = "\033[38;2;224;175;104m" if COLORS_AVAILABLE else ""
    ORANGE = "\033[38;2;255;158;100m" if COLORS_AVAILABLE else ""
    RED = "\033[38;2;247;118;142m" if COLORS_AVAILABLE else ""
    PINK = "\033[38;2;187;154;247m" if COLORS_AVAILABLE else ""

    # Special effects
    BOLD = "\033[1m" if COLORS_AVAILABLE else ""
    DIM = "\033[2m" if COLORS_AVAILABLE else ""
    ITALIC = "\033[3m" if COLORS_AVAILABLE else ""
    UNDERLINE = "\033[4m" if COLORS_AVAILABLE else ""
    BLINK = "\033[5m" if COLORS_AVAILABLE else ""
    RESET = "\033[0m" if COLORS_AVAILABLE else ""

    # Gradient effects
    GRADIENT_START = "\033[38;2;187;154;247m" if COLORS_AVAILABLE else ""
    GRADIENT_END = "\033[38;2;125;207;255m" if COLORS_AVAILABLE else ""

class InteractiveTUI:
    """Enhanced interactive TUI with Tokyo Night theme"""

    def __init__(self):
        self.theme = TokyoNightTheme()
        self.width = self._get_terminal_width()
        self.animation_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.current_animation = 0

    def _get_terminal_width(self):
        """Get terminal width for responsive design"""
        try:
            return os.get_terminal_size().columns
        except:
            return 80

    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def print_gradient_text(self, text: str, start_color: str = None, end_color: str = None):
        """Print text with gradient effect"""
        if not COLORS_AVAILABLE:
            print(text)
            return

        start_color = start_color or self.theme.GRADIENT_START
        end_color = end_color or self.theme.GRADIENT_END

        # Simple gradient simulation
        mid_point = len(text) // 2
        gradient_text = start_color + text[:mid_point] + end_color + text[mid_point:] + self.theme.RESET
        print(gradient_text)

    def print_box(self, content: str, title: str = "", box_type: str = "info"):
        """Print content in a styled box"""
        box_colors = {
            "info": self.theme.BLUE,
            "success": self.theme.GREEN,
            "warning": self.theme.YELLOW,
            "error": self.theme.RED,
            "accent": self.theme.PURPLE
        }

        color = box_colors.get(box_type, self.theme.BLUE)

        # Box drawing characters
        top_left = "╭"
        top_right = "╮"
        bottom_left = "╰"
        bottom_right = "╯"
        horizontal = "─"
        vertical = "│"

        lines = content.split('\n')
        max_width = max(len(line) for line in lines) if lines else 0
        box_width = min(max_width + 4, self.width - 4)

        # Top border
        if title:
            title_display = f" {title} "
            title_padding = (box_width - len(title_display) - 2) // 2
            top_border = f"{color}{top_left}{horizontal * title_padding}{title_display}{horizontal * (box_width - len(title_display) - title_padding - 2)}{top_right}{self.theme.RESET}"
        else:
            top_border = f"{color}{top_left}{horizontal * (box_width - 2)}{top_right}{self.theme.RESET}"

        print(top_border)

        # Content
        for line in lines:
            padding = box_width - len(line) - 4
            print(f"{color}{vertical}{self.theme.RESET} {line}{' ' * padding} {color}{vertical}{self.theme.RESET}")

        # Bottom border
        bottom_border = f"{color}{bottom_left}{horizontal * (box_width - 2)}{bottom_right}{self.theme.RESET}"
        print(bottom_border)

    def print_status_bar(self, left_text: str, right_text: str = ""):
        """Print a status bar at the bottom"""
        available_width = self.width - len(left_text) - len(right_text) - 2
        padding = " " * max(0, available_width)

        status_bar = f"{self.theme.BG_LIGHT}{self.theme.FG_WHITE} {left_text}{padding}{right_text} {self.theme.RESET}"
        print(status_bar)

    def animate_loading(self, text: str, duration: float = 2.0):
        """Show animated loading indicator"""
        start_time = time.time()
        while time.time() - start_time < duration:
            char = self.animation_chars[self.current_animation % len(self.animation_chars)]
            print(f"\r{self.theme.CYAN}{char}{self.theme.RESET} {text}", end="", flush=True)
            self.current_animation += 1
            time.sleep(0.1)
        print(f"\r{self.theme.GREEN}✓{self.theme.RESET} {text}")

    def print_header(self, title: str, subtitle: str = ""):
        """Print Claude Code style minimal header"""
        self.clear_screen()

        # Claude Code style minimal header
        print(f"{self.theme.CYAN}claude{self.theme.RESET}")
        print(f"{self.theme.FG_GRAY}Claude Code - Elite Coding Agent v3.0{self.theme.RESET}")

        if subtitle:
            print(f"{self.theme.FG_DARK_GRAY}{subtitle}{self.theme.RESET}")

        print()
        print(f"{self.theme.FG_GRAY}Type your request or use commands like /help, /status, /clear{self.theme.RESET}")
        print(f"{self.theme.FG_DARK_GRAY}Press Ctrl+C to exit{self.theme.RESET}")
        print()

    def print_claude_thinking(self, message: str = "Thinking..."):
        """Print Claude Code style thinking indicator"""
        print(f"{self.theme.FG_DARK_GRAY}● {message}{self.theme.RESET}", end="", flush=True)

    def print_claude_response_start(self):
        """Print Claude Code style response start"""
        print(f"\n{self.theme.FG_WHITE}", end="", flush=True)

    def print_menu(self, options: List[str], selected: int = -1):
        """Print interactive menu"""
        for i, option in enumerate(options):
            if i == selected:
                print(f"{self.theme.BG_LIGHT}{self.theme.FG_WHITE} ▶ {option} {self.theme.RESET}")
            else:
                print(f"   {self.theme.FG_GRAY}{option}{self.theme.RESET}")

    def get_styled_input(self, prompt: str = "") -> str:
        """Get user input with Claude Code style prompt"""
        # Claude Code style simple prompt
        styled_prompt = f"{self.theme.FG_GRAY}> {self.theme.RESET}"
        return input(styled_prompt)

    def typewriter_print(self, text: str, delay: float = 0.005, color: str = None):
        """Print text with fast typewriter effect"""
        if not COLORS_AVAILABLE:
            print(text)
            return

        color = color or self.theme.FG_WHITE

        for char in text:
            print(f"{color}{char}{self.theme.RESET}", end="", flush=True)
            if char not in ' \t\n':  # Don't delay on whitespace for better flow
                time.sleep(delay)
        print()  # New line at the end

    def typewriter_print_box(self, content: str, title: str = "", box_type: str = "info", delay: float = 0.01):
        """Print a box with typewriter effect content"""
        box_colors = {
            "info": self.theme.BLUE,
            "success": self.theme.GREEN,
            "warning": self.theme.YELLOW,
            "error": self.theme.RED,
            "accent": self.theme.PURPLE
        }

        color = box_colors.get(box_type, self.theme.BLUE)

        # Box drawing characters
        top_left = "╭"
        top_right = "╮"
        bottom_left = "╰"
        bottom_right = "╯"
        horizontal = "─"
        vertical = "│"

        lines = content.split('\n')
        max_width = max(len(line) for line in lines) if lines else 0
        box_width = min(max_width + 4, self.width - 4)

        # Top border
        if title:
            title_display = f" {title} "
            title_padding = (box_width - len(title_display) - 2) // 2
            top_border = f"{color}{top_left}{horizontal * title_padding}{title_display}{horizontal * (box_width - len(title_display) - title_padding - 2)}{top_right}{self.theme.RESET}"
        else:
            top_border = f"{color}{top_left}{horizontal * (box_width - 2)}{top_right}{self.theme.RESET}"

        print(top_border)

        # Content with typewriter effect
        for line in lines:
            padding = box_width - len(line) - 4
            print(f"{color}{vertical}{self.theme.RESET} ", end="", flush=True)

            # Typewriter effect for content
            for char in line:
                print(char, end="", flush=True)
                if char != ' ':
                    time.sleep(delay)

            print(f"{' ' * padding} {color}{vertical}{self.theme.RESET}")

        # Bottom border
        bottom_border = f"{color}{bottom_left}{horizontal * (box_width - 2)}{bottom_right}{self.theme.RESET}"
        print(bottom_border)

    def stream_response(self, text: str, delay: float = 0.008):
        """Stream response text with fast typewriter effect and smart formatting"""
        if not text:
            return

        # For very fast streaming like GPTme
        for char in text:
            if char == '\n':
                print()
                time.sleep(delay * 2)  # Brief pause at line breaks
            else:
                # Color coding for different content
                if char in '•-':
                    print(f"{self.theme.YELLOW}{char}{self.theme.RESET}", end="", flush=True)
                elif char in '`':
                    print(f"{self.theme.CYAN}{char}{self.theme.RESET}", end="", flush=True)
                else:
                    print(f"{self.theme.FG_WHITE}{char}{self.theme.RESET}", end="", flush=True)

                # Very fast delay, only on non-whitespace
                if char not in ' \t':
                    time.sleep(delay)

        print()  # Final newline

# Initialize Gemini AI
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    google_api_key=os.getenv("GEMINI_API_KEY"),
    temperature=0.1,
    streaming=True,
    callbacks=[StreamingStdOutCallbackHandler()]
)

# Enhanced Context and State Management
@dataclass
class TaskMemory:
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    status: str = "pending"  # pending, in_progress, completed, failed
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)
    result: Optional[str] = None
    error: Optional[str] = None

@dataclass
class CodeChunk:
    content: str
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str  # function, class, block, etc.
    metadata: Dict = field(default_factory=dict)

@dataclass
class AgentContext:
    current_directory: str = os.getcwd()
    active_files: List[str] = field(default_factory=list)
    command_history: List[str] = field(default_factory=list)
    project_structure: Dict = field(default_factory=dict)
    last_error: str = ""
    working_memory: Dict = field(default_factory=dict)
    task_queue: List[TaskMemory] = field(default_factory=list)
    code_chunks: Dict[str, CodeChunk] = field(default_factory=dict)
    user_preferences: Dict = field(default_factory=dict)
    session_stats: Dict = field(default_factory=dict)
    background_tasks: List = field(default_factory=list)
    cached_results: Dict = field(default_factory=dict)
    intent_history: List[str] = field(default_factory=list)

    def __post_init__(self):
        if not self.session_stats:
            self.session_stats = {
                "commands_executed": 0,
                "files_modified": 0,
                "errors_fixed": 0,
                "tests_run": 0,
                "start_time": datetime.now()
            }

class SmartOrchestrator:
    """Smart orchestration engine for managing complex tasks"""

    def __init__(self, agent_context: AgentContext):
        self.context = agent_context
        self.task_graph = {}
        self.execution_queue = []

    def analyze_intent(self, user_input: str) -> Dict[str, Any]:
        """Analyze user intent and predict next actions"""
        intent_patterns = {
            "create_project": r"create|build|make.*(?:app|project|website)",
            "fix_error": r"fix|debug|error|bug|issue",
            "refactor": r"refactor|optimize|improve|clean",
            "test": r"test|testing|unit test|integration",
            "deploy": r"deploy|deployment|production|publish",
            "analyze": r"analyze|review|check|examine",
            "search": r"find|search|look for|locate"
        }

        detected_intents = []
        for intent, pattern in intent_patterns.items():
            if re.search(pattern, user_input, re.IGNORECASE):
                detected_intents.append(intent)

        return {
            "primary_intent": detected_intents[0] if detected_intents else "general",
            "all_intents": detected_intents,
            "complexity": len(user_input.split()),
            "requires_planning": any(word in user_input.lower() for word in ["complex", "full", "complete", "entire"])
        }

    def create_execution_plan(self, intent_analysis: Dict, user_input: str) -> List[TaskMemory]:
        """Create smart execution plan based on intent"""
        tasks = []

        if intent_analysis["primary_intent"] == "create_project":
            tasks.extend([
                TaskMemory(description="Analyze project requirements", status="pending"),
                TaskMemory(description="Setup project structure", status="pending"),
                TaskMemory(description="Install dependencies", status="pending"),
                TaskMemory(description="Create initial files", status="pending"),
                TaskMemory(description="Run initial tests", status="pending")
            ])
        elif intent_analysis["primary_intent"] == "fix_error":
            tasks.extend([
                TaskMemory(description="Identify error source", status="pending"),
                TaskMemory(description="Analyze error context", status="pending"),
                TaskMemory(description="Generate fix solution", status="pending"),
                TaskMemory(description="Apply fix", status="pending"),
                TaskMemory(description="Verify fix works", status="pending")
            ])

        return tasks

class AdvancedCodingAgent:
    def __init__(self):
        self.context = AgentContext()
        self.orchestrator = SmartOrchestrator(self.context)
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=8)
        self.memory = ConversationBufferWindowMemory(k=20, return_messages=True)
        self.cache = {}
        self.running_processes = {}
        self.background_monitor = None
        self.tui = InteractiveTUI()
        self.setup_logging()

    def setup_logging(self):
        """Setup minimal logging system (disabled for better performance)"""
        # Disable most logging for better performance
        logging.basicConfig(level=logging.ERROR)
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.ERROR)

    def start_background_monitor(self):
        """Start background monitoring for system resources and errors"""
        def monitor():
            while True:
                try:
                    # Monitor system resources
                    cpu_percent = psutil.cpu_percent()
                    memory_percent = psutil.virtual_memory().percent

                    if cpu_percent > 80 or memory_percent > 85:
                        self.logger.warning(f"High resource usage: CPU {cpu_percent}%, Memory {memory_percent}%")

                    # Check for file changes in active files
                    for file_path in self.context.active_files:
                        if os.path.exists(file_path):
                            mtime = os.path.getmtime(file_path)
                            if file_path not in self.context.cached_results or \
                               self.context.cached_results[file_path].get('mtime', 0) < mtime:
                                self.context.cached_results[file_path] = {'mtime': mtime}
                                self.logger.info(f"File changed: {file_path}")

                    time.sleep(5)  # Check every 5 seconds
                except Exception as e:
                    self.logger.error(f"Background monitor error: {e}")
                    time.sleep(10)

        if not self.background_monitor:
            self.background_monitor = threading.Thread(target=monitor, daemon=True)
            self.background_monitor.start()

    # 🧠 Text & Code Manipulation Tools
    def replace_string_in_file(self, file_path: str, old_string: str, new_string: str, use_regex: bool = False) -> str:
        """Replace specific string or regex pattern in a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if use_regex:
                new_content = re.sub(old_string, new_string, content)
                replacements = len(re.findall(old_string, content))
            else:
                new_content = content.replace(old_string, new_string)
                replacements = content.count(old_string)

            if replacements > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                return f"✅ Replaced {replacements} occurrences in {file_path}"
            else:
                return f"❌ No matches found for '{old_string}' in {file_path}"

        except Exception as e:
            return f"❌ Error replacing string: {str(e)}"

    def replace_in_multiple_files(self, pattern: str, replacement: str, directory: str = ".",
                                file_pattern: str = "*.py", use_regex: bool = True) -> str:
        """Find and replace across multiple files in a directory"""
        try:
            results = []
            files_modified = 0

            for file_path in glob.glob(os.path.join(directory, "**", file_pattern), recursive=True):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    if use_regex:
                        new_content = re.sub(pattern, replacement, content)
                        matches = len(re.findall(pattern, content))
                    else:
                        new_content = content.replace(pattern, replacement)
                        matches = content.count(pattern)

                    if matches > 0:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        results.append(f"  {file_path}: {matches} replacements")
                        files_modified += 1

                except Exception as e:
                    results.append(f"  ❌ Error in {file_path}: {str(e)}")

            if files_modified > 0:
                return f"✅ Modified {files_modified} files:\n" + "\n".join(results)
            else:
                return f"❌ No matches found for pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error in multi-file replace: {str(e)}"

    def insert_text_at_position(self, file_path: str, text: str, line_number: int) -> str:
        """Insert text at a specific line number"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_number < 1 or line_number > len(lines) + 1:
                return f"❌ Invalid line number {line_number}. File has {len(lines)} lines"

            lines.insert(line_number - 1, text + '\n')

            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            return f"✅ Inserted text at line {line_number} in {file_path}"

        except Exception as e:
            return f"❌ Error inserting text: {str(e)}"

    def insert_before_after(self, file_path: str, text: str, target_line: str,
                          position: str = "after") -> str:
        """Insert text before or after a matching line"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            insertions = 0
            new_lines = []

            for line in lines:
                if position == "before" and target_line in line:
                    new_lines.append(text + '\n')
                    insertions += 1

                new_lines.append(line)

                if position == "after" and target_line in line:
                    new_lines.append(text + '\n')
                    insertions += 1

            if insertions > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                return f"✅ Inserted text {position} {insertions} matching lines in {file_path}"
            else:
                return f"❌ No matching lines found for '{target_line}'"

        except Exception as e:
            return f"❌ Error inserting text: {str(e)}"

    def delete_lines_matching(self, file_path: str, pattern: str, use_regex: bool = True) -> str:
        """Delete all lines matching a specific pattern"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            original_count = len(lines)

            if use_regex:
                filtered_lines = [line for line in lines if not re.search(pattern, line)]
            else:
                filtered_lines = [line for line in lines if pattern not in line]

            deleted_count = original_count - len(filtered_lines)

            if deleted_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(filtered_lines)
                return f"✅ Deleted {deleted_count} lines matching pattern in {file_path}"
            else:
                return f"❌ No lines found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error deleting lines: {str(e)}"

    def delete_line_range(self, file_path: str, start_line: int, end_line: int) -> str:
        """Delete lines from start_line to end_line (inclusive)"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if start_line < 1 or end_line > len(lines) or start_line > end_line:
                return f"❌ Invalid line range {start_line}-{end_line}. File has {len(lines)} lines"

            deleted_lines = lines[start_line-1:end_line]
            remaining_lines = lines[:start_line-1] + lines[end_line:]

            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(remaining_lines)

            return f"✅ Deleted lines {start_line}-{end_line} ({len(deleted_lines)} lines) from {file_path}"

        except Exception as e:
            return f"❌ Error deleting line range: {str(e)}"

    def append_text_to_file(self, file_path: str, text: str) -> str:
        """Add text to the end of a file"""
        try:
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write('\n' + text)
            return f"✅ Appended text to {file_path}"
        except Exception as e:
            return f"❌ Error appending text: {str(e)}"

    def prepend_text_to_file(self, file_path: str, text: str) -> str:
        """Add text to the beginning of a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(text + '\n' + content)

            return f"✅ Prepended text to {file_path}"
        except Exception as e:
            return f"❌ Error prepending text: {str(e)}"

    def comment_out_matching_lines(self, file_path: str, pattern: str, comment_style: str = "#") -> str:
        """Comment out all lines matching a pattern"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            commented_count = 0
            new_lines = []

            for line in lines:
                if re.search(pattern, line) and not line.strip().startswith(comment_style):
                    new_lines.append(f"{comment_style} {line}")
                    commented_count += 1
                else:
                    new_lines.append(line)

            if commented_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                return f"✅ Commented out {commented_count} lines in {file_path}"
            else:
                return f"❌ No lines found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error commenting lines: {str(e)}"

    def uncomment_lines(self, file_path: str, comment_style: str = "#") -> str:
        """Uncomment previously commented lines"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            uncommented_count = 0
            new_lines = []

            for line in lines:
                stripped = line.strip()
                if stripped.startswith(comment_style + " "):
                    new_lines.append(line.replace(comment_style + " ", "", 1))
                    uncommented_count += 1
                elif stripped.startswith(comment_style):
                    new_lines.append(line.replace(comment_style, "", 1))
                    uncommented_count += 1
                else:
                    new_lines.append(line)

            if uncommented_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                return f"✅ Uncommented {uncommented_count} lines in {file_path}"
            else:
                return f"❌ No commented lines found"

        except Exception as e:
            return f"❌ Error uncommenting lines: {str(e)}"

    def extract_function(self, file_path: str, function_pattern: str, new_function_name: str) -> str:
        """Extract code block and convert into a reusable function"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find the code block to extract
            lines = content.split('\n')
            start_line = None
            end_line = None
            indent_level = None

            for i, line in enumerate(lines):
                if re.search(function_pattern, line):
                    start_line = i
                    indent_level = len(line) - len(line.lstrip())
                    break

            if start_line is None:
                return f"❌ Pattern '{function_pattern}' not found"

            # Find the end of the block based on indentation
            for i in range(start_line + 1, len(lines)):
                line = lines[i]
                if line.strip() and (len(line) - len(line.lstrip())) <= indent_level:
                    end_line = i - 1
                    break

            if end_line is None:
                end_line = len(lines) - 1

            # Extract the code block
            extracted_code = '\n'.join(lines[start_line:end_line + 1])

            # Create new function
            function_code = f"\ndef {new_function_name}():\n"
            for line in lines[start_line:end_line + 1]:
                function_code += "    " + line + "\n"

            # Replace original code with function call
            new_lines = lines[:start_line] + [f"    {new_function_name}()"] + lines[end_line + 1:]
            new_content = '\n'.join(new_lines) + function_code

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return f"✅ Extracted function '{new_function_name}' from {file_path}"

        except Exception as e:
            return f"❌ Error extracting function: {str(e)}"

    # 🔍 Advanced Search and Analysis Tools
    def semantic_code_search(self, query: str, directory: str = ".") -> str:
        """Search code using natural language queries"""
        try:
            results = []

            # Keywords extraction from natural language query
            keywords = re.findall(r'\b\w+\b', query.lower())

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Score based on keyword matches
                            score = 0
                            matches = []

                            for keyword in keywords:
                                if keyword in content.lower():
                                    score += content.lower().count(keyword)
                                    # Find specific lines
                                    lines = content.split('\n')
                                    for i, line in enumerate(lines, 1):
                                        if keyword in line.lower():
                                            matches.append(f"  Line {i}: {line.strip()}")

                            if score > 0:
                                results.append({
                                    'file': file_path,
                                    'score': score,
                                    'matches': matches[:3]  # Top 3 matches
                                })
                        except:
                            continue

            # Sort by relevance score
            results.sort(key=lambda x: x['score'], reverse=True)

            if results:
                output = f"🔍 Semantic search results for '{query}':\n\n"
                for result in results[:5]:  # Top 5 files
                    output += f"📁 {result['file']} (score: {result['score']}):\n"
                    output += "\n".join(result['matches']) + "\n\n"
                return output
            else:
                return f"❌ No relevant code found for query '{query}'"

        except Exception as e:
            return f"❌ Error in semantic search: {str(e)}"

    def grep_search(self, pattern: str, directory: str = ".", file_types: List[str] = None) -> str:
        """Advanced grep-like search with context"""
        try:
            if file_types is None:
                file_types = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json", "*.md"]

            results = []

            for file_type in file_types:
                for file_path in glob.glob(os.path.join(directory, "**", file_type), recursive=True):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()

                        matches = []
                        for i, line in enumerate(lines):
                            if re.search(pattern, line, re.IGNORECASE):
                                # Add context lines
                                context_start = max(0, i - 2)
                                context_end = min(len(lines), i + 3)
                                context = []

                                for j in range(context_start, context_end):
                                    prefix = ">>> " if j == i else "    "
                                    context.append(f"{prefix}{j+1}: {lines[j].rstrip()}")

                                matches.append("\n".join(context))

                        if matches:
                            results.append(f"📁 {file_path}:\n" + "\n\n".join(matches))

                    except:
                        continue

            if results:
                return f"🔍 Grep search results for '{pattern}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No matches found for pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error in grep search: {str(e)}"

    def smart_text_replace(self, file_path: str, description: str) -> str:
        """LLM-based meaning-aware text replacement"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            prompt = f"""Analyze this code and make the following change: {description}

Original code:
```
{content}
```

Please provide the modified code with the requested changes. Maintain the same structure and style."""

            response = llm.invoke([HumanMessage(content=prompt)])
            modified_code = response.content

            # Extract code from response
            code_match = re.search(r'```(?:\w+)?\n(.*?)\n```', modified_code, re.DOTALL)
            if code_match:
                new_content = code_match.group(1)

                # Create backup
                backup_path = f"{file_path}.backup_{int(time.time())}"
                shutil.copy2(file_path, backup_path)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                return f"✅ Smart replacement completed in {file_path}\n🔄 Backup saved as {backup_path}"
            else:
                return f"❌ Could not extract modified code from AI response"

        except Exception as e:
            return f"❌ Error in smart text replace: {str(e)}"

    def highlight_code_block(self, file_path: str, pattern: str) -> str:
        """Highlight and extract matching code blocks for review"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            lines = content.split('\n')
            highlighted_blocks = []

            for i, line in enumerate(lines):
                if re.search(pattern, line, re.IGNORECASE):
                    # Find the complete block (function, class, etc.)
                    start_line = i
                    indent_level = len(line) - len(line.lstrip())

                    # Find block boundaries
                    block_start = start_line
                    block_end = start_line

                    # Look backwards for block start
                    for j in range(start_line - 1, -1, -1):
                        if lines[j].strip() and (len(lines[j]) - len(lines[j].lstrip())) < indent_level:
                            if any(keyword in lines[j] for keyword in ['def ', 'class ', 'if ', 'for ', 'while ']):
                                block_start = j
                            break

                    # Look forwards for block end
                    for j in range(start_line + 1, len(lines)):
                        if lines[j].strip() and (len(lines[j]) - len(lines[j].lstrip())) <= indent_level:
                            block_end = j - 1
                            break
                    else:
                        block_end = len(lines) - 1

                    block_content = '\n'.join(lines[block_start:block_end + 1])
                    highlighted_blocks.append({
                        'start': block_start + 1,
                        'end': block_end + 1,
                        'content': block_content
                    })

            if highlighted_blocks:
                result = f"🔍 Highlighted code blocks matching '{pattern}' in {file_path}:\n\n"
                for i, block in enumerate(highlighted_blocks[:5], 1):
                    result += f"Block {i} (lines {block['start']}-{block['end']}):\n"
                    result += "```\n" + block['content'] + "\n```\n\n"
                return result
            else:
                return f"❌ No code blocks found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error highlighting code blocks: {str(e)}"

    # 🧪 Testing and Debugging Tools
    def test_search(self, source_file: str) -> str:
        """Find tests related to a source file"""
        try:
            base_name = os.path.splitext(os.path.basename(source_file))[0]
            test_patterns = [
                f"test_{base_name}.py",
                f"{base_name}_test.py",
                f"test{base_name}.py",
                f"tests/{base_name}.py",
                f"test/test_{base_name}.py"
            ]

            found_tests = []
            for pattern in test_patterns:
                for test_file in glob.glob(f"**/{pattern}", recursive=True):
                    if os.path.exists(test_file):
                        found_tests.append(test_file)

            if found_tests:
                result = f"🧪 Found test files for {source_file}:\n"
                for test_file in found_tests:
                    result += f"  • {test_file}\n"
                return result
            else:
                return f"❌ No test files found for {source_file}"

        except Exception as e:
            return f"❌ Error searching tests: {str(e)}"

    def autonomous_debugger(self, error_message: str, code_context: str = "") -> str:
        """AI-powered debugging with trace analysis"""
        try:
            debug_prompt = f"""Analyze this error and provide debugging steps:

Error: {error_message}

Code Context:
{code_context}

Please provide:
1. Root cause analysis
2. Step-by-step debugging approach
3. Potential fixes
4. Prevention strategies"""

            response = llm.invoke([HumanMessage(content=debug_prompt)])

            # Also try to automatically detect common issues
            auto_fixes = []

            if "ModuleNotFoundError" in error_message:
                module_match = re.search(r"No module named '(\w+)'", error_message)
                if module_match:
                    auto_fixes.append(f"pip install {module_match.group(1)}")

            if "SyntaxError" in error_message:
                auto_fixes.append("Check for missing parentheses, brackets, or quotes")

            if "IndentationError" in error_message:
                auto_fixes.append("Fix indentation - use consistent spaces or tabs")

            result = f"🐛 Autonomous Debugging Analysis:\n\n{response.content}"

            if auto_fixes:
                result += f"\n\n🔧 Quick Fixes:\n" + "\n".join([f"• {fix}" for fix in auto_fixes])

            return result

        except Exception as e:
            return f"❌ Error in autonomous debugging: {str(e)}"

    def lint_check(self, file_path: str, language: str = "python") -> str:
        """Run comprehensive linting and static analysis"""
        try:
            results = []

            if language.lower() == "python":
                # Check for common Python issues
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                issues = []
                lines = content.split('\n')

                for i, line in enumerate(lines, 1):
                    # Check for common issues
                    if len(line) > 100:
                        issues.append(f"Line {i}: Line too long ({len(line)} chars)")

                    if re.search(r'\t', line):
                        issues.append(f"Line {i}: Contains tabs (use spaces)")

                    if re.search(r'print\(', line) and not line.strip().startswith('#'):
                        issues.append(f"Line {i}: Debug print statement found")

                    if re.search(r'TODO|FIXME|HACK', line, re.IGNORECASE):
                        issues.append(f"Line {i}: TODO/FIXME comment found")

                # Try to run actual linting tools if available
                try:
                    result = subprocess.run(['python', '-m', 'flake8', file_path],
                                          capture_output=True, text=True, timeout=10)
                    if result.stdout:
                        issues.extend(result.stdout.split('\n'))
                except:
                    pass

                if issues:
                    return f"🔍 Lint check results for {file_path}:\n" + "\n".join([f"• {issue}" for issue in issues if issue])
                else:
                    return f"✅ No linting issues found in {file_path}"

            else:
                return f"🔍 Basic lint check for {language} (limited support)"

        except Exception as e:
            return f"❌ Error in lint check: {str(e)}"

    def self_repair(self, file_path: str) -> str:
        """AI-suggested automatic code repairs"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            repair_prompt = f"""Analyze this code and suggest automatic repairs for common issues:

Code:
```
{content}
```

Please identify and fix:
1. Syntax errors
2. Import issues
3. Indentation problems
4. Common bugs
5. Code style issues

Provide the corrected code:"""

            response = llm.invoke([HumanMessage(content=repair_prompt)])

            # Extract corrected code
            code_match = re.search(r'```(?:\w+)?\n(.*?)\n```', response.content, re.DOTALL)
            if code_match:
                corrected_code = code_match.group(1)

                # Create backup
                backup_path = f"{file_path}.backup_{int(time.time())}"
                shutil.copy2(file_path, backup_path)

                # Apply repairs
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(corrected_code)

                return f"🔧 Self-repair completed for {file_path}\n📝 Analysis: {response.content[:200]}...\n🔄 Backup: {backup_path}"
            else:
                return f"🔍 Repair analysis: {response.content}"

        except Exception as e:
            return f"❌ Error in self-repair: {str(e)}"

    # 🧠 AI-Powered Intelligence Tools
    def natural_language_to_code(self, description: str, language: str = "python") -> str:
        """Convert natural language description to code"""
        try:
            prompt = f"""Convert this natural language description to {language} code:

Description: {description}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comments for complex logic
- Follow {language} best practices
- Make it modular and testable

Generated {language} code:"""

            response = llm.invoke([HumanMessage(content=prompt)])

            # Extract code from response
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', response.content, re.DOTALL)
            if code_match:
                generated_code = code_match.group(1)

                # Save to file with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_{language}_{timestamp}.{self._get_file_extension(language)}"

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(generated_code)

                return f"🤖 Generated {language} code saved to {filename}:\n```{language}\n{generated_code}\n```"
            else:
                return f"🤖 Generated {language} code:\n{response.content}"

        except Exception as e:
            return f"❌ Error generating code: {str(e)}"

    def _get_file_extension(self, language: str) -> str:
        """Get file extension for programming language"""
        extensions = {
            "python": "py",
            "javascript": "js",
            "typescript": "ts",
            "java": "java",
            "cpp": "cpp",
            "c": "c",
            "csharp": "cs",
            "go": "go",
            "rust": "rs",
            "php": "php",
            "ruby": "rb"
        }
        return extensions.get(language.lower(), "txt")

    def intent_recognition(self, user_input: str) -> str:
        """Understand and categorize user intent"""
        try:
            intent_analysis = self.orchestrator.analyze_intent(user_input)

            result = f"🧠 Intent Analysis for: '{user_input}'\n\n"
            result += f"Primary Intent: {intent_analysis['primary_intent']}\n"
            result += f"All Detected Intents: {', '.join(intent_analysis['all_intents'])}\n"
            result += f"Complexity Score: {intent_analysis['complexity']}\n"
            result += f"Requires Planning: {intent_analysis['requires_planning']}\n"

            # Add to intent history
            self.context.intent_history.append({
                'input': user_input,
                'analysis': intent_analysis,
                'timestamp': datetime.now()
            })

            return result

        except Exception as e:
            return f"❌ Error in intent recognition: {str(e)}"

    # 🔧 Advanced Workflow Tools
    def create_new_workspace(self, project_name: str, project_type: str = "python") -> str:
        """Setup a complete development workspace"""
        try:
            workspace_path = os.path.join(os.getcwd(), project_name)
            os.makedirs(workspace_path, exist_ok=True)

            # Create basic structure based on project type
            if project_type.lower() == "python":
                structure = {
                    "src": {},
                    "tests": {},
                    "docs": {},
                    "requirements.txt": "# Add your dependencies here\n",
                    "README.md": f"# {project_name}\n\nProject description here.\n",
                    ".gitignore": "__pycache__/\n*.pyc\n.env\nvenv/\n",
                    "main.py": f'"""\n{project_name} - Main module\n"""\n\ndef main():\n    print("Hello, {project_name}!")\n\nif __name__ == "__main__":\n    main()\n'
                }
            elif project_type.lower() in ["javascript", "node", "react"]:
                structure = {
                    "src": {},
                    "public": {},
                    "tests": {},
                    "package.json": f'{{\n  "name": "{project_name}",\n  "version": "1.0.0",\n  "description": "",\n  "main": "index.js",\n  "scripts": {{\n    "start": "node index.js",\n    "test": "jest"\n  }}\n}}',
                    "README.md": f"# {project_name}\n\nProject description here.\n",
                    ".gitignore": "node_modules/\n.env\ndist/\nbuild/\n",
                    "index.js": f'// {project_name} - Main entry point\nconsole.log("Hello, {project_name}!");\n'
                }
            else:
                structure = {
                    "src": {},
                    "docs": {},
                    "README.md": f"# {project_name}\n\nProject description here.\n"
                }

            # Create the structure
            for path, content in structure.items():
                full_path = os.path.join(workspace_path, path)
                if isinstance(content, dict):
                    os.makedirs(full_path, exist_ok=True)
                else:
                    os.makedirs(os.path.dirname(full_path), exist_ok=True)
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)

            # Change to new workspace
            os.chdir(workspace_path)
            self.context.current_directory = workspace_path

            return f"✅ Created {project_type} workspace '{project_name}' at {workspace_path}"

        except Exception as e:
            return f"❌ Error creating workspace: {str(e)}"

    def multi_step_loop(self, task_description: str, max_iterations: int = 5) -> str:
        """Execute a multi-step task with iterative improvement"""
        try:
            results = []
            current_task = task_description

            for iteration in range(max_iterations):
                print(f"🔄 Iteration {iteration + 1}/{max_iterations}: {current_task}")

                # Generate code or solution
                if "code" in current_task.lower():
                    result = self.generate_code(current_task)
                elif "test" in current_task.lower():
                    result = self.run_tests(".")
                elif "fix" in current_task.lower():
                    result = self.fix_errors(self.context.last_error, current_task)
                else:
                    result = f"Processed: {current_task}"

                results.append(f"Step {iteration + 1}: {result}")

                # Check if task is complete
                if "✅" in result and "error" not in result.lower():
                    break

                # Refine task for next iteration
                current_task = f"Improve and fix issues in: {current_task}"

            return f"🔄 Multi-step execution completed:\n" + "\n".join(results)

        except Exception as e:
            return f"❌ Error in multi-step loop: {str(e)}"

    def code_duplication_removal(self, directory: str = ".") -> str:
        """Detect and remove code duplication"""
        try:
            duplicates = []
            file_contents = {}

            # Read all Python files
            for file_path in glob.glob(os.path.join(directory, "**", "*.py"), recursive=True):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        file_contents[file_path] = content.split('\n')
                except:
                    continue

            # Find duplicate code blocks
            for file1, lines1 in file_contents.items():
                for file2, lines2 in file_contents.items():
                    if file1 >= file2:  # Avoid duplicate comparisons
                        continue

                    # Find common subsequences
                    matcher = difflib.SequenceMatcher(None, lines1, lines2)
                    matches = matcher.get_matching_blocks()

                    for match in matches:
                        if match.size > 5:  # Only consider blocks > 5 lines
                            duplicate_lines = lines1[match.a:match.a + match.size]
                            duplicates.append({
                                'file1': file1,
                                'file2': file2,
                                'lines': match.size,
                                'content': '\n'.join(duplicate_lines[:3]) + '...'
                            })

            if duplicates:
                result = f"🔍 Found {len(duplicates)} code duplications:\n"
                for i, dup in enumerate(duplicates[:10], 1):
                    result += f"\n{i}. {dup['file1']} ↔ {dup['file2']} ({dup['lines']} lines)\n"
                    result += f"   Preview: {dup['content']}\n"
                return result
            else:
                return "✅ No significant code duplication found"

        except Exception as e:
            return f"❌ Error detecting duplicates: {str(e)}"

    def context_aware_refactor(self, file_path: str, refactor_goal: str) -> str:
        """Smart refactoring based on context and goals"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Analyze current code structure
            analysis = self.analyze_code(content)

            refactor_prompt = f"""Perform context-aware refactoring on this code:

Goal: {refactor_goal}
Current Analysis: {analysis}

Original Code:
```python
{content}
```

Please refactor the code to achieve the goal while:
1. Maintaining functionality
2. Improving code quality
3. Following best practices
4. Adding appropriate comments
5. Ensuring testability

Refactored code:"""

            response = llm.invoke([HumanMessage(content=refactor_prompt)])

            # Extract refactored code
            code_match = re.search(r'```(?:python)?\n(.*?)\n```', response.content, re.DOTALL)
            if code_match:
                refactored_code = code_match.group(1)

                # Create backup
                backup_path = f"{file_path}.backup_{int(time.time())}"
                shutil.copy2(file_path, backup_path)

                # Apply refactoring
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(refactored_code)

                return f"🔄 Context-aware refactoring completed for {file_path}\n🎯 Goal: {refactor_goal}\n🔄 Backup: {backup_path}\n📝 Analysis: {response.content[:200]}..."
            else:
                return f"🔍 Refactoring analysis: {response.content}"

        except Exception as e:
            return f"❌ Error in context-aware refactoring: {str(e)}"

    # 🌐 Web and Network Tools
    def fetch_webpage(self, url: str) -> str:
        """Fetch and return webpage content"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Basic HTML cleaning
            content = response.text
            # Remove script and style elements
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content).strip()

            return f"✅ Fetched content from {url}:\n{content[:2000]}{'...' if len(content) > 2000 else ''}"

        except Exception as e:
            return f"❌ Error fetching webpage: {str(e)}"

    def install_python_packages(self, packages: str) -> str:
        """Install Python packages using pip"""
        try:
            package_list = [pkg.strip() for pkg in packages.split(',')]
            results = []

            for package in package_list:
                if not package:
                    continue

                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', package],
                    capture_output=True,
                    text=True,
                    timeout=300
                )

                if result.returncode == 0:
                    results.append(f"✅ {package}: Installed successfully")
                else:
                    results.append(f"❌ {package}: {result.stderr.strip()}")

            return f"📦 Package installation results:\n" + "\n".join(results)

        except Exception as e:
            return f"❌ Error installing packages: {str(e)}"

    def get_changed_files(self, directory: str = ".") -> str:
        """Get Git diff of modified files"""
        try:
            # Check if it's a git repository
            git_check = subprocess.run(['git', 'status'], capture_output=True, text=True, cwd=directory)
            if git_check.returncode != 0:
                return "❌ Not a Git repository"

            # Get status of files
            status_result = subprocess.run(['git', 'status', '--porcelain'], capture_output=True, text=True, cwd=directory)

            if not status_result.stdout.strip():
                return "✅ No changes detected in Git repository"

            # Get detailed diff
            diff_result = subprocess.run(['git', 'diff', 'HEAD'], capture_output=True, text=True, cwd=directory)

            status_lines = status_result.stdout.strip().split('\n')
            changed_files = []

            for line in status_lines:
                if len(line) >= 3:
                    status = line[:2]
                    filename = line[3:]
                    changed_files.append(f"  {status} {filename}")

            result = f"📝 Git Changes Summary:\n" + "\n".join(changed_files)

            if diff_result.stdout:
                result += f"\n\n🔍 Detailed Diff:\n{diff_result.stdout[:1500]}{'...' if len(diff_result.stdout) > 1500 else ''}"

            return result

        except Exception as e:
            return f"❌ Error getting Git changes: {str(e)}"

    def list_code_usages(self, symbol: str, directory: str = ".") -> str:
        """Find where symbols (functions/classes) are used"""
        try:
            results = []

            for root, dirs, files in os.walk(directory):
                # Skip common directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'env']]

                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs', '.go', '.rs')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()

                            matches = []
                            for i, line in enumerate(lines, 1):
                                if symbol in line:
                                    # Check if it's not just a substring
                                    if re.search(rf'\b{re.escape(symbol)}\b', line):
                                        matches.append(f"    Line {i}: {line.strip()}")

                            if matches:
                                results.append(f"📁 {file_path}:\n" + "\n".join(matches[:5]))

                        except:
                            continue

            if results:
                return f"🔍 Found {len(results)} files using '{symbol}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No usages found for symbol '{symbol}'"

        except Exception as e:
            return f"❌ Error searching for symbol usages: {str(e)}"

    def get_errors(self, file_path: str = None) -> str:
        """Get lint, syntax, or compiler errors"""
        try:
            errors = []

            if file_path:
                files_to_check = [file_path]
            else:
                # Check all Python files in current directory
                files_to_check = glob.glob("**/*.py", recursive=True)

            for file in files_to_check:
                try:
                    # Check Python syntax
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    try:
                        ast.parse(content)
                    except SyntaxError as e:
                        errors.append(f"❌ {file}: Syntax Error at line {e.lineno}: {e.msg}")

                    # Try running flake8 if available
                    try:
                        result = subprocess.run(['python', '-m', 'flake8', file],
                                              capture_output=True, text=True, timeout=10)
                        if result.stdout:
                            for line in result.stdout.strip().split('\n'):
                                if line:
                                    errors.append(f"⚠️  {line}")
                    except:
                        pass

                except Exception as e:
                    errors.append(f"❌ Error checking {file}: {str(e)}")

            if errors:
                return f"🐛 Found {len(errors)} errors:\n" + "\n".join(errors[:20])
            else:
                return "✅ No errors found"

        except Exception as e:
            return f"❌ Error checking for errors: {str(e)}"

    # 📁 Additional File System Tools
    def create_file(self, file_path: str, content: str) -> str:
        """Create a file with content"""
        return self.write_file(file_path, content)

    def create_directory(self, directory_path: str) -> str:
        """Create nested folders/directories"""
        try:
            os.makedirs(directory_path, exist_ok=True)
            return f"✅ Directory created: {directory_path}"
        except Exception as e:
            return f"❌ Error creating directory: {str(e)}"

    def list_dir(self, directory: str = ".") -> str:
        """List files/folders in a directory"""
        try:
            items = []
            for item in sorted(os.listdir(directory)):
                item_path = os.path.join(directory, item)
                if os.path.isdir(item_path):
                    items.append(f"📁 {item}/")
                else:
                    size = os.path.getsize(item_path)
                    items.append(f"📄 {item} ({size} bytes)")

            return f"📂 Contents of {directory}:\n" + "\n".join(items)
        except Exception as e:
            return f"❌ Error listing directory: {str(e)}"

    def file_search(self, pattern: str, directory: str = ".") -> str:
        """Search files by glob patterns"""
        try:
            matches = glob.glob(os.path.join(directory, pattern), recursive=True)
            if matches:
                return f"🔍 Found {len(matches)} files matching '{pattern}':\n" + "\n".join([f"  • {match}" for match in matches[:20]])
            else:
                return f"❌ No files found matching pattern '{pattern}'"
        except Exception as e:
            return f"❌ Error in file search: {str(e)}"

    def get_project_setup_info(self, directory: str = ".") -> str:
        """Detect framework, language, tooling, etc."""
        try:
            info = {
                "languages": [],
                "frameworks": [],
                "tools": [],
                "config_files": []
            }

            # Check for common files
            common_files = {
                "package.json": ("JavaScript/Node.js", ["React", "Vue", "Angular"]),
                "requirements.txt": ("Python", ["Django", "Flask", "FastAPI"]),
                "Cargo.toml": ("Rust", []),
                "go.mod": ("Go", []),
                "pom.xml": ("Java", ["Spring", "Maven"]),
                "build.gradle": ("Java", ["Spring", "Gradle"]),
                "composer.json": ("PHP", ["Laravel", "Symfony"]),
                "Gemfile": ("Ruby", ["Rails"]),
                ".gitignore": ("Git", []),
                "Dockerfile": ("Docker", []),
                "docker-compose.yml": ("Docker Compose", [])
            }

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file in common_files:
                        lang, frameworks = common_files[file]
                        if lang not in info["languages"]:
                            info["languages"].append(lang)
                        info["frameworks"].extend(frameworks)
                        info["config_files"].append(os.path.join(root, file))

                    # Check file extensions
                    ext = os.path.splitext(file)[1]
                    ext_langs = {
                        ".py": "Python", ".js": "JavaScript", ".ts": "TypeScript",
                        ".java": "Java", ".cpp": "C++", ".c": "C", ".cs": "C#",
                        ".go": "Go", ".rs": "Rust", ".php": "PHP", ".rb": "Ruby"
                    }
                    if ext in ext_langs and ext_langs[ext] not in info["languages"]:
                        info["languages"].append(ext_langs[ext])

            result = f"🔍 Project Setup Analysis for {directory}:\n"
            result += f"Languages: {', '.join(info['languages']) or 'None detected'}\n"
            result += f"Frameworks: {', '.join(set(info['frameworks'])) or 'None detected'}\n"
            result += f"Config Files: {len(info['config_files'])} found\n"

            if info['config_files']:
                result += "Key Files:\n" + "\n".join([f"  • {f}" for f in info['config_files'][:10]])

            return result

        except Exception as e:
            return f"❌ Error analyzing project setup: {str(e)}"

    # 🧪 Additional Testing Tools
    def test_failure(self, error_output: str) -> str:
        """Capture and analyze test failure messages"""
        try:
            analysis = {
                "error_type": "Unknown",
                "suggestions": []
            }

            # Common test failure patterns
            if "AssertionError" in error_output:
                analysis["error_type"] = "Assertion Failure"
                analysis["suggestions"].append("Check expected vs actual values")
            elif "ImportError" in error_output or "ModuleNotFoundError" in error_output:
                analysis["error_type"] = "Import Error"
                analysis["suggestions"].append("Check module installation and paths")
            elif "AttributeError" in error_output:
                analysis["error_type"] = "Attribute Error"
                analysis["suggestions"].append("Check object attributes and method names")
            elif "TypeError" in error_output:
                analysis["error_type"] = "Type Error"
                analysis["suggestions"].append("Check function arguments and types")

            result = f"🧪 Test Failure Analysis:\n"
            result += f"Error Type: {analysis['error_type']}\n"
            result += f"Suggestions:\n" + "\n".join([f"  • {s}" for s in analysis['suggestions']])
            result += f"\n\nOriginal Error:\n{error_output[:500]}{'...' if len(error_output) > 500 else ''}"

            return result

        except Exception as e:
            return f"❌ Error analyzing test failure: {str(e)}"

    def code_linting_static_analysis(self, directory: str = ".") -> str:
        """Combine all error & code checkers"""
        try:
            results = []

            # Run multiple checks
            results.append("🔍 Running comprehensive code analysis...")

            # Lint check
            lint_result = self.lint_check(directory)
            results.append(f"\n📋 Linting Results:\n{lint_result}")

            # Error check
            error_result = self.get_errors()
            results.append(f"\n🐛 Error Check:\n{error_result}")

            # Project structure analysis
            structure_result = self.get_project_setup_info(directory)
            results.append(f"\n🏗️ Project Structure:\n{structure_result}")

            return "\n".join(results)

        except Exception as e:
            return f"❌ Error in comprehensive analysis: {str(e)}"

    # 🧩 Advanced Code Manipulation Tools
    def toggle_comments(self, file_path: str, pattern: str, comment_style: str = "#") -> str:
        """Toggle comments on/off for matched lines"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            modified_count = 0
            new_lines = []

            for line in lines:
                if re.search(pattern, line):
                    stripped = line.strip()
                    if stripped.startswith(comment_style):
                        # Uncomment
                        new_lines.append(line.replace(comment_style + " ", "", 1))
                        modified_count += 1
                    else:
                        # Comment
                        new_lines.append(f"{comment_style} {line}")
                        modified_count += 1
                else:
                    new_lines.append(line)

            if modified_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                return f"✅ Toggled comments on {modified_count} lines in {file_path}"
            else:
                return f"❌ No lines found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error toggling comments: {str(e)}"

    def inline_function(self, file_path: str, function_name: str) -> str:
        """Replace function call with full code logic inline"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find function definition
            function_pattern = rf'def\s+{re.escape(function_name)}\s*\([^)]*\):'
            function_match = re.search(function_pattern, content, re.MULTILINE)

            if not function_match:
                return f"❌ Function '{function_name}' not found"

            lines = content.split('\n')
            start_line = content[:function_match.start()].count('\n')

            # Extract function body
            function_body = []
            indent_level = None

            for i in range(start_line + 1, len(lines)):
                line = lines[i]
                if not line.strip():
                    continue

                current_indent = len(line) - len(line.lstrip())

                if indent_level is None:
                    indent_level = current_indent

                if current_indent <= indent_level and line.strip():
                    break

                function_body.append(line[indent_level:])  # Remove function indentation

            # Find function calls and replace
            call_pattern = rf'{re.escape(function_name)}\s*\([^)]*\)'
            calls = list(re.finditer(call_pattern, content))

            if not calls:
                return f"❌ No calls to function '{function_name}' found"

            # Replace calls with function body
            new_content = content
            for call in reversed(calls):  # Reverse to maintain positions
                call_indent = len(content[:call.start()].split('\n')[-1]) - len(content[:call.start()].split('\n')[-1].lstrip())
                indented_body = '\n'.join([' ' * call_indent + line for line in function_body])
                new_content = new_content[:call.start()] + indented_body + new_content[call.end():]

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return f"✅ Inlined {len(calls)} calls to function '{function_name}' in {file_path}"

        except Exception as e:
            return f"❌ Error inlining function: {str(e)}"

    def rename_symbol_in_file(self, file_path: str, old_name: str, new_name: str) -> str:
        """Rename variable/function/class inside a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Use word boundaries to avoid partial matches
            pattern = rf'\b{re.escape(old_name)}\b'
            matches = len(re.findall(pattern, content))

            if matches == 0:
                return f"❌ Symbol '{old_name}' not found in {file_path}"

            new_content = re.sub(pattern, new_name, content)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return f"✅ Renamed '{old_name}' to '{new_name}' ({matches} occurrences) in {file_path}"

        except Exception as e:
            return f"❌ Error renaming symbol: {str(e)}"

    def rename_symbol_project_wide(self, old_name: str, new_name: str, directory: str = ".") -> str:
        """Rename symbol across project with semantic awareness"""
        try:
            results = []
            total_changes = 0

            for root, dirs, files in os.walk(directory):
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]

                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c')):
                        file_path = os.path.join(root, file)
                        result = self.rename_symbol_in_file(file_path, old_name, new_name)

                        if "✅" in result:
                            results.append(result)
                            # Extract number of changes
                            match = re.search(r'\((\d+) occurrences\)', result)
                            if match:
                                total_changes += int(match.group(1))

            if results:
                return f"✅ Project-wide rename completed: {total_changes} total changes\n" + "\n".join(results[:10])
            else:
                return f"❌ Symbol '{old_name}' not found in project"

        except Exception as e:
            return f"❌ Error in project-wide rename: {str(e)}"

    def move_block_to_new_file(self, source_file: str, target_file: str, block_pattern: str) -> str:
        """Extract a code block and write to a new module/file"""
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Find the block to move
            start_line = None
            end_line = None

            for i, line in enumerate(lines):
                if re.search(block_pattern, line):
                    start_line = i
                    indent_level = len(line) - len(line.lstrip())

                    # Find end of block
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip() and (len(lines[j]) - len(lines[j].lstrip())) <= indent_level:
                            end_line = j - 1
                            break
                    else:
                        end_line = len(lines) - 1
                    break

            if start_line is None:
                return f"❌ Block matching '{block_pattern}' not found"

            # Extract the block
            extracted_block = ''.join(lines[start_line:end_line + 1])

            # Write to target file
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(extracted_block)

            # Remove from source file
            remaining_lines = lines[:start_line] + lines[end_line + 1:]
            with open(source_file, 'w', encoding='utf-8') as f:
                f.writelines(remaining_lines)

            return f"✅ Moved code block to {target_file} and removed from {source_file}"

        except Exception as e:
            return f"❌ Error moving code block: {str(e)}"

    # 🧩 Chunk-Level Editing Tools
    def split_code_by_function(self, file_path: str) -> str:
        """Break full code into logical function chunks"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find all function definitions
            function_pattern = r'^(def\s+\w+.*?:)'
            functions = []
            lines = content.split('\n')

            current_function = None
            current_lines = []

            for i, line in enumerate(lines):
                if re.match(function_pattern, line.strip()):
                    # Save previous function
                    if current_function:
                        functions.append({
                            'name': current_function,
                            'lines': current_lines,
                            'start': i - len(current_lines),
                            'end': i - 1
                        })

                    # Start new function
                    current_function = line.strip()
                    current_lines = [line]
                elif current_function:
                    # Check if we're still in the function
                    if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                        # End of function
                        functions.append({
                            'name': current_function,
                            'lines': current_lines,
                            'start': i - len(current_lines),
                            'end': i - 1
                        })
                        current_function = None
                        current_lines = []
                    else:
                        current_lines.append(line)

            # Add last function if exists
            if current_function:
                functions.append({
                    'name': current_function,
                    'lines': current_lines,
                    'start': len(lines) - len(current_lines),
                    'end': len(lines) - 1
                })

            if functions:
                result = f"📊 Found {len(functions)} functions in {file_path}:\n"
                for func in functions:
                    result += f"  • {func['name']} (lines {func['start']}-{func['end']}, {len(func['lines'])} lines)\n"
                return result
            else:
                return f"❌ No functions found in {file_path}"

        except Exception as e:
            return f"❌ Error splitting code by function: {str(e)}"

    def extract_code_chunk(self, file_path: str, start_marker: str, end_marker: str) -> str:
        """Extract specific region from start-end marker"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            start_line = None
            end_line = None

            for i, line in enumerate(lines):
                if start_marker in line and start_line is None:
                    start_line = i
                elif end_marker in line and start_line is not None:
                    end_line = i
                    break

            if start_line is None:
                return f"❌ Start marker '{start_marker}' not found"
            if end_line is None:
                return f"❌ End marker '{end_marker}' not found"

            extracted_chunk = ''.join(lines[start_line:end_line + 1])

            # Save to a new file
            chunk_file = f"{file_path}.chunk_{int(time.time())}"
            with open(chunk_file, 'w', encoding='utf-8') as f:
                f.write(extracted_chunk)

            return f"✅ Extracted chunk (lines {start_line}-{end_line}) to {chunk_file}\n\nContent:\n{extracted_chunk[:500]}{'...' if len(extracted_chunk) > 500 else ''}"

        except Exception as e:
            return f"❌ Error extracting code chunk: {str(e)}"

    def merge_code_chunks(self, file1: str, file2: str, output_file: str) -> str:
        """Merge multiple blocks or files together"""
        try:
            content1 = ""
            content2 = ""

            if os.path.exists(file1):
                with open(file1, 'r', encoding='utf-8') as f:
                    content1 = f.read()

            if os.path.exists(file2):
                with open(file2, 'r', encoding='utf-8') as f:
                    content2 = f.read()

            # Merge with separator
            merged_content = content1 + "\n\n# === MERGED FROM " + file2 + " ===\n\n" + content2

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(merged_content)

            return f"✅ Merged {file1} and {file2} into {output_file} ({len(merged_content)} characters)"

        except Exception as e:
            return f"❌ Error merging code chunks: {str(e)}"

    def diff_two_chunks(self, chunk1: str, chunk2: str) -> str:
        """Compare differences between two code blocks"""
        try:
            lines1 = chunk1.split('\n')
            lines2 = chunk2.split('\n')

            diff = list(difflib.unified_diff(lines1, lines2, lineterm='', fromfile='chunk1', tofile='chunk2'))

            if diff:
                return f"🔍 Differences found:\n" + "\n".join(diff[:50])
            else:
                return "✅ No differences found between chunks"

        except Exception as e:
            return f"❌ Error comparing chunks: {str(e)}"

    # 🧠 AI Reasoning Tools
    def chain_of_thought_reasoning(self, problem: str) -> str:
        """Break down complex steps with reasoning"""
        try:
            reasoning_prompt = f"""Break down this problem using chain-of-thought reasoning:

Problem: {problem}

Please provide:
1. Problem analysis
2. Step-by-step breakdown
3. Key considerations
4. Potential solutions
5. Recommended approach

Use clear reasoning at each step:"""

            response = llm.invoke([HumanMessage(content=reasoning_prompt)])

            return f"🧠 Chain-of-Thought Analysis:\n\n{response.content}"

        except Exception as e:
            return f"❌ Error in chain-of-thought reasoning: {str(e)}"

    def self_critique(self, solution: str, original_problem: str) -> str:
        """Evaluate own answers and improve results"""
        try:
            critique_prompt = f"""Critically evaluate this solution:

Original Problem: {original_problem}

Proposed Solution: {solution}

Please provide:
1. Strengths of the solution
2. Potential weaknesses or issues
3. Missing considerations
4. Suggestions for improvement
5. Alternative approaches
6. Overall assessment (1-10 scale)

Be thorough and constructive in your critique:"""

            response = llm.invoke([HumanMessage(content=critique_prompt)])

            return f"🔍 Self-Critique Analysis:\n\n{response.content}"

        except Exception as e:
            return f"❌ Error in self-critique: {str(e)}"

    # 🌐 Enhanced Web and Browser Tools
    def open_simple_browser(self, url: str) -> str:
        """Open webpage in browser"""
        try:
            import webbrowser
            webbrowser.open(url)
            return f"✅ Opened {url} in default browser"
        except Exception as e:
            return f"❌ Error opening browser: {str(e)}"

    def github_repo_search(self, query: str) -> str:
        """Search GitHub repositories"""
        try:
            # Use GitHub API to search repositories
            api_url = f"https://api.github.com/search/repositories?q={urllib.parse.quote(query)}&sort=stars&order=desc"

            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'Advanced-Coding-Agent'
            }

            response = requests.get(api_url, headers=headers, timeout=10)
            response.raise_for_status()

            data = response.json()
            repos = data.get('items', [])[:5]  # Top 5 results

            if repos:
                result = f"🔍 Found {len(repos)} GitHub repositories for '{query}':\n\n"
                for repo in repos:
                    result += f"📁 {repo['full_name']}\n"
                    result += f"   ⭐ {repo['stargazers_count']} stars | 🍴 {repo['forks_count']} forks\n"
                    result += f"   📝 {repo['description'] or 'No description'}\n"
                    result += f"   🔗 {repo['html_url']}\n\n"
                return result
            else:
                return f"❌ No repositories found for query '{query}'"

        except Exception as e:
            return f"❌ Error searching GitHub: {str(e)}"

    def semantic_web_search(self, query: str, num_results: int = 5) -> str:
        """Natural language Google/Bing/Web search"""
        try:
            # Use a simple web search approach
            search_url = f"https://www.google.com/search?q={urllib.parse.quote(query)}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()

            # Basic parsing of search results (simplified)
            content = response.text

            # Extract basic information
            result = f"🔍 Web search results for '{query}':\n\n"
            result += f"Search URL: {search_url}\n"
            result += f"Response received: {len(content)} characters\n"
            result += f"Status: Search completed successfully\n\n"
            result += "Note: For detailed results, use the fetch_webpage tool with specific URLs."

            return result

        except Exception as e:
            return f"❌ Error in web search: {str(e)}"

    def configure_python_environment(self, env_name: str, python_version: str = "3.9") -> str:
        """Setup and manage Python virtual environment"""
        try:
            commands = []
            results = []

            # Create virtual environment
            if os.name == 'nt':  # Windows
                commands.extend([
                    f"python -m venv {env_name}",
                    f"{env_name}\\Scripts\\activate.bat && python --version",
                    f"{env_name}\\Scripts\\activate.bat && pip install --upgrade pip"
                ])
            else:  # Unix/Linux/Mac
                commands.extend([
                    f"python{python_version} -m venv {env_name}",
                    f"source {env_name}/bin/activate && python --version",
                    f"source {env_name}/bin/activate && pip install --upgrade pip"
                ])

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        results.append(f"✅ {cmd}: Success")
                    else:
                        results.append(f"❌ {cmd}: {result.stderr.strip()}")
                except subprocess.TimeoutExpired:
                    results.append(f"⏰ {cmd}: Timeout")
                except Exception as e:
                    results.append(f"❌ {cmd}: {str(e)}")

            return f"🐍 Python Environment Setup for '{env_name}':\n" + "\n".join(results)

        except Exception as e:
            return f"❌ Error configuring Python environment: {str(e)}"

    # 🔧 Augment Agent Tools Integration
    def augment_codebase_retrieval(self, query: str) -> str:
        """Use Augment's codebase retrieval for semantic code search"""
        try:
            # Simulate Augment's codebase retrieval functionality
            result = f"🔍 Augment Codebase Retrieval for: '{query}'\n\n"
            result += "Searching across codebase with semantic understanding...\n"

            # Search in current directory for relevant code
            matches = []
            for root, dirs, files in os.walk("."):
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Simple semantic matching
                            query_words = query.lower().split()
                            score = 0
                            for word in query_words:
                                score += content.lower().count(word)

                            if score > 0:
                                matches.append({
                                    'file': file_path,
                                    'score': score,
                                    'preview': content[:200] + '...'
                                })
                        except:
                            continue

            matches.sort(key=lambda x: x['score'], reverse=True)

            if matches:
                result += f"Found {len(matches)} relevant files:\n\n"
                for match in matches[:5]:
                    result += f"📁 {match['file']} (relevance: {match['score']})\n"
                    result += f"Preview: {match['preview']}\n\n"
            else:
                result += "No relevant code found in current codebase."

            return result

        except Exception as e:
            return f"❌ Error in codebase retrieval: {str(e)}"

    def augment_web_search(self, query: str, num_results: int = 5) -> str:
        """Enhanced web search with Augment-style results"""
        try:
            # Use the existing web search but enhance the results
            basic_result = self.semantic_web_search(query, num_results)

            enhanced_result = f"🌐 Augment Web Search for: '{query}'\n\n"
            enhanced_result += "Searching with advanced semantic understanding...\n\n"
            enhanced_result += basic_result

            # Add additional context
            enhanced_result += f"\n\n💡 Search Insights:\n"
            enhanced_result += f"• Query analyzed for technical context\n"
            enhanced_result += f"• Results filtered for coding relevance\n"
            enhanced_result += f"• Cross-referenced with best practices\n"

            return enhanced_result

        except Exception as e:
            return f"❌ Error in enhanced web search: {str(e)}"

    def augment_code_generation(self, description: str, language: str = "python", style: str = "professional") -> str:
        """Advanced code generation with Augment-style intelligence"""
        try:
            enhanced_prompt = f"""Generate {language} code with Augment-level intelligence:

Description: {description}
Language: {language}
Style: {style}

Requirements:
- Follow best practices and design patterns
- Include comprehensive error handling
- Add detailed comments and documentation
- Ensure code is production-ready
- Consider security and performance
- Use modern language features
- Include type hints where applicable

Generated {language} code:"""

            response = llm.invoke([HumanMessage(content=enhanced_prompt)])

            # Extract and enhance the code
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', response.content, re.DOTALL)
            if code_match:
                generated_code = code_match.group(1)

                # Save to file with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"augment_generated_{language}_{timestamp}.{self._get_file_extension(language)}"

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(generated_code)

                result = f"🤖 Augment Code Generation Complete!\n\n"
                result += f"Generated {language} code saved to: {filename}\n\n"
                result += f"Code Preview:\n```{language}\n{generated_code[:500]}{'...' if len(generated_code) > 500 else ''}\n```\n\n"
                result += f"Features:\n"
                result += f"• Production-ready code with best practices\n"
                result += f"• Comprehensive error handling\n"
                result += f"• Detailed documentation\n"
                result += f"• Security and performance optimized\n"

                return result
            else:
                return f"🤖 Augment Code Analysis:\n{response.content}"

        except Exception as e:
            return f"❌ Error in Augment code generation: {str(e)}"

    def augment_diagnostics(self, file_paths: List[str]) -> str:
        """Get comprehensive diagnostics like Augment's diagnostics tool"""
        try:
            if not file_paths:
                file_paths = [f for f in os.listdir('.') if f.endswith(('.py', '.js', '.ts'))]

            results = []
            total_issues = 0

            for file_path in file_paths:
                if not os.path.exists(file_path):
                    continue

                file_issues = []

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Syntax check
                    if file_path.endswith('.py'):
                        try:
                            ast.parse(content)
                        except SyntaxError as e:
                            file_issues.append(f"Syntax Error (line {e.lineno}): {e.msg}")

                    # Common issues check
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if len(line) > 120:
                            file_issues.append(f"Line {i}: Line too long ({len(line)} chars)")
                        if 'TODO' in line or 'FIXME' in line:
                            file_issues.append(f"Line {i}: TODO/FIXME comment found")
                        if re.search(r'print\s*\(', line) and not line.strip().startswith('#'):
                            file_issues.append(f"Line {i}: Debug print statement")

                    if file_issues:
                        results.append(f"📁 {file_path} ({len(file_issues)} issues):")
                        results.extend([f"  • {issue}" for issue in file_issues[:10]])
                        total_issues += len(file_issues)
                    else:
                        results.append(f"✅ {file_path}: No issues found")

                except Exception as e:
                    results.append(f"❌ {file_path}: Error analyzing - {str(e)}")

            summary = f"🔍 Augment Diagnostics Report\n\n"
            summary += f"Files analyzed: {len(file_paths)}\n"
            summary += f"Total issues found: {total_issues}\n\n"
            summary += "\n".join(results)

            return summary

        except Exception as e:
            return f"❌ Error in Augment diagnostics: {str(e)}"

    def analyze_intent(self, user_input: str) -> Dict:
        """Analyze user intent for smart orchestration"""
        try:
            # Analyze the user input to understand intent
            intent_analysis = {
                "primary_goal": "code_assistance",
                "complexity": "medium",
                "requires_planning": False,
                "estimated_steps": 1,
                "tools_needed": [],
                "context_required": True
            }

            # Simple intent classification
            if any(word in user_input.lower() for word in ["create", "build", "develop", "make"]):
                intent_analysis["primary_goal"] = "creation"
                intent_analysis["complexity"] = "high"
                intent_analysis["requires_planning"] = True
                intent_analysis["estimated_steps"] = 3
            elif any(word in user_input.lower() for word in ["fix", "debug", "error", "bug"]):
                intent_analysis["primary_goal"] = "debugging"
                intent_analysis["tools_needed"] = ["autonomous_debugger", "lint_check", "get_errors"]
            elif any(word in user_input.lower() for word in ["refactor", "optimize", "improve"]):
                intent_analysis["primary_goal"] = "optimization"
                intent_analysis["tools_needed"] = ["context_aware_refactor", "analyze_code"]
            elif any(word in user_input.lower() for word in ["test", "testing"]):
                intent_analysis["primary_goal"] = "testing"
                intent_analysis["tools_needed"] = ["run_tests", "test_search"]

            return intent_analysis

        except Exception as e:
            return {"error": f"Intent analysis failed: {str(e)}"}

    def create_execution_plan(self, intent_analysis: Dict, user_input: str) -> List:
        """Create smart execution plan based on intent"""
        try:
            plan = []

            if intent_analysis.get("primary_goal") == "creation":
                plan = [
                    {"step": 1, "action": "analyze_requirements", "description": "Understand what needs to be created"},
                    {"step": 2, "action": "design_structure", "description": "Plan the code structure and architecture"},
                    {"step": 3, "action": "implement_code", "description": "Write the actual code"},
                    {"step": 4, "action": "test_and_validate", "description": "Test the implementation"}
                ]
            elif intent_analysis.get("primary_goal") == "debugging":
                plan = [
                    {"step": 1, "action": "identify_error", "description": "Locate and understand the error"},
                    {"step": 2, "action": "analyze_root_cause", "description": "Find the root cause of the issue"},
                    {"step": 3, "action": "implement_fix", "description": "Apply the necessary fix"}
                ]
            elif intent_analysis.get("primary_goal") == "optimization":
                plan = [
                    {"step": 1, "action": "analyze_current_code", "description": "Understand current implementation"},
                    {"step": 2, "action": "identify_improvements", "description": "Find optimization opportunities"},
                    {"step": 3, "action": "refactor_code", "description": "Apply improvements"}
                ]
            else:
                plan = [
                    {"step": 1, "action": "understand_request", "description": "Analyze the user request"},
                    {"step": 2, "action": "execute_solution", "description": "Implement the solution"}
                ]

            return plan

        except Exception as e:
            return [{"error": f"Plan creation failed: {str(e)}"}]

    def execute_task(self, task: Dict, context: str = "") -> str:
        """Execute a specific task from the execution plan"""
        try:
            action = task.get("action", "unknown")
            description = task.get("description", "No description")

            result = f"🔄 Executing: {description}\n"

            if action == "analyze_requirements":
                result += "📋 Analyzing requirements and specifications...\n"
                result += "✅ Requirements analysis complete"
            elif action == "design_structure":
                result += "🏗️ Designing code structure and architecture...\n"
                result += "✅ Structure design complete"
            elif action == "implement_code":
                result += "💻 Implementing the code solution...\n"
                result += "✅ Code implementation complete"
            elif action == "test_and_validate":
                result += "🧪 Testing and validating the solution...\n"
                result += "✅ Testing and validation complete"
            elif action == "identify_error":
                result += "🔍 Identifying and analyzing errors...\n"
                result += "✅ Error identification complete"
            elif action == "analyze_root_cause":
                result += "🎯 Analyzing root cause of the issue...\n"
                result += "✅ Root cause analysis complete"
            elif action == "implement_fix":
                result += "🔧 Implementing the fix...\n"
                result += "✅ Fix implementation complete"
            else:
                result += f"⚙️ Executing {action}...\n"
                result += "✅ Task execution complete"

            return result

        except Exception as e:
            return f"❌ Task execution failed: {str(e)}"

    def get_system_info(self) -> str:
        """Get comprehensive system information"""
        try:
            import platform
            info = {
                "os": platform.system(),
                "version": platform.version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "current_dir": os.getcwd(),
                "user": os.getenv("USERNAME") or os.getenv("USER", "unknown")
            }
            return json.dumps(info, indent=2)
        except Exception as e:
            return f"Error getting system info: {str(e)}"

    def run_command(self, command: str, timeout: int = 30) -> str:
        """Execute PowerShell commands with advanced error handling"""
        try:
            self.context.command_history.append(command)

            # Use PowerShell for Windows
            if os.name == 'nt':
                cmd = ['powershell', '-Command', command]
            else:
                cmd = command

            result = subprocess.run(
                cmd,
                shell=True if os.name != 'nt' else False,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.context.current_directory
            )

            output = result.stdout.strip() if result.stdout else ""
            error = result.stderr.strip() if result.stderr else ""

            if result.returncode == 0:
                return f"✅ Command executed successfully:\n{output}"
            else:
                self.context.last_error = error
                return f"❌ Command failed (code {result.returncode}):\n{error}\nOutput: {output}"

        except subprocess.TimeoutExpired:
            return f"⏰ Command timed out after {timeout} seconds"
        except Exception as e:
            error_msg = f"❌ Error executing command: {str(e)}"
            self.context.last_error = error_msg
            return error_msg
    def write_file(self, path: str, content: str, backup: bool = True) -> str:
        """Advanced file writing with backup and validation"""
        try:
            abs_path = os.path.abspath(path)
            dir_path = os.path.dirname(abs_path)

            # Create backup if file exists
            if backup and os.path.exists(abs_path):
                backup_path = f"{abs_path}.backup_{int(time.time())}"
                shutil.copy2(abs_path, backup_path)

            # Ensure directory exists
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write file with encoding
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            # Validate written content
            with open(abs_path, 'r', encoding='utf-8') as f:
                written_content = f.read()

            if written_content == content:
                return f"✅ File '{path}' written successfully ({len(content)} chars)"
            else:
                return f"⚠️ File written but content validation failed"

        except Exception as e:
            return f"❌ Error writing file '{path}': {str(e)}"


    def read_file(self, file_path: str, lines: Optional[Tuple[int, int]] = None) -> str:
        """Advanced file reading with line range support"""
        try:
            abs_path = os.path.abspath(file_path)

            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"

            with open(abs_path, 'r', encoding='utf-8') as f:
                if lines:
                    all_lines = f.readlines()
                    start, end = lines
                    selected_lines = all_lines[start-1:end] if end != -1 else all_lines[start-1:]
                    content = ''.join(selected_lines)
                else:
                    content = f.read()

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            return f"✅ File content ({len(content)} chars):\n{content}"

        except Exception as e:
            return f"❌ Error reading file '{file_path}': {str(e)}"
    

    def search_files(self, pattern: str, directory: str = ".", file_types: List[str] = None) -> str:
        """Search for files and content with advanced filtering"""
        try:
            if file_types is None:
                file_types = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json", "*.md", "*.txt", "*.yml", "*.yaml", "*.sh", "*.bash", "*.ps1", "*.cmd", "*.bat", "*.ini", "*.cfg", "*.conf", "*.xml", "*.csv", "*.log"]

            results = []
            search_dir = os.path.abspath(directory)

            for file_type in file_types:    
                for file_path in glob.glob(os.path.join(search_dir, "**", file_type), recursive=True):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if re.search(pattern, content, re.IGNORECASE):
                                # Find matching lines
                                lines = content.split('\n')
                                matches = []
                                for i, line in enumerate(lines, 1):
                                    if re.search(pattern, line, re.IGNORECASE):
                                        matches.append(f"  Line {i}: {line.strip()}")

                                results.append(f"📁 {file_path}:\n" + "\n".join(matches[:5]))
                    except:
                        continue

            if results:
                return f"🔍 Found {len(results)} files matching '{pattern}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No files found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error searching files: {str(e)}"

    def get_web_info(self, query: str) -> str:
        """Retrieve information from web without search engine API"""
        try:
            # Simple web scraping for documentation and info
            encoded_query = urllib.parse.quote(query)
            urls = [
                f"https://docs.python.org/3/search.html?q={encoded_query}",
                f"https://developer.mozilla.org/en-US/search?q={encoded_query}",
                f"https://stackoverflow.com/search?q={encoded_query}"
            ]

            results = []
            for url in urls[:2]:  # Limit to avoid rate limiting
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        content = response.read().decode('utf-8')
                        # Extract useful text (simplified)
                        text_content = re.sub(r'<[^>]+>', '', content)
                        text_content = re.sub(r'\s+', ' ', text_content)
                        results.append(text_content[:500] + "...")
                except:
                    continue

            if results:
                return f"🌐 Web information for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ Could not retrieve web information for '{query}'"

        except Exception as e:
            return f"❌ Error retrieving web info: {str(e)}"

    def analyze_code(self, code: str, language: str = "python") -> str:
        """Advanced code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = {
                        "functions": [],
                        "classes": [],
                        "imports": [],
                        "variables": [],
                        "complexity": 0
                    }

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis["functions"].append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis["classes"].append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis["imports"].append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis["imports"].append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis["variables"].append(target.id)

                    return f"📊 Code Analysis:\n{json.dumps(analysis, indent=2)}"
                except SyntaxError as e:
                    return f"❌ Syntax Error in code: {str(e)}"
            else:
                return f"🔍 Basic analysis for {language} code:\nLines: {len(code.split())}\nCharacters: {len(code)}"

        except Exception as e:
            return f"❌ Error analyzing code: {str(e)}"

    def fix_errors(self, error_log: str, code_context: str = "") -> str:
        """Advanced error analysis and fixing suggestions"""
        try:
            suggestions = []
            fixes = []

            # Common error patterns and fixes
            error_patterns = {
                r"ModuleNotFoundError.*'(\w+)'": lambda m: f"pip install {m.group(1)}",
                r"SyntaxError.*line (\d+)": lambda m: f"Check syntax on line {m.group(1)}",
                r"IndentationError": lambda m: "Fix indentation - use consistent spaces/tabs",
                r"NameError.*'(\w+)'": lambda m: f"Variable '{m.group(1)}' not defined - check spelling",
                r"FileNotFoundError.*'([^']+)'": lambda m: f"File '{m.group(1)}' not found - check path",
                r"port.*already in use": lambda m: "Change port number or kill existing process",
                r"Permission denied": lambda m: "Run with administrator privileges or check file permissions"
            }

            for pattern, fix_func in error_patterns.items():
                matches = re.finditer(pattern, error_log, re.IGNORECASE)
                for match in matches:
                    fix = fix_func(match)
                    if fix not in fixes:
                        fixes.append(fix)

            # AI-powered suggestions based on context
            if code_context:
                if "import" in error_log.lower() and "module" in error_log.lower():
                    missing_modules = re.findall(r"No module named '(\w+)'", error_log)
                    for module in missing_modules:
                        fixes.append(f"Install missing module: pip install {module}")

            if fixes:
                return f"🔧 Error Analysis & Fixes:\n" + "\n".join([f"• {fix}" for fix in fixes])
            else:
                return f"🤔 Complex error detected. Manual review needed:\n{error_log[:500]}"

        except Exception as e:
            return f"❌ Error analyzing error log: {str(e)}"

    def generate_code(self, description: str, language: str = "python") -> str:
        """AI-powered code generation"""
        try:
            prompt = f"""Generate {language} code for: {description}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comments for complex logic
- Follow best practices for {language}
- Make it modular and reusable

Code:"""

            # Use LangChain to generate code
            response = llm.invoke([HumanMessage(content=prompt)])
            generated_code = response.content

            # Extract code from response
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', generated_code, re.DOTALL)
            if code_match:
                return f"🤖 Generated {language} code:\n```{language}\n{code_match.group(1)}\n```"
            else:
                return f"🤖 Generated {language} code:\n{generated_code}"

        except Exception as e:
            return f"❌ Error generating code: {str(e)}"

    def refactor_code(self, code: str, refactor_type: str = "optimize") -> str:
        """AI-powered code refactoring"""
        try:
            refactor_prompts = {
                "optimize": "Optimize this code for better performance and readability",
                "modularize": "Break this code into smaller, reusable functions/modules",
                "clean": "Clean up this code - remove duplicates, improve naming, add comments",
                "secure": "Make this code more secure - fix potential vulnerabilities"
            }

            prompt = f"""{refactor_prompts.get(refactor_type, refactor_prompts['optimize'])}:

Original Code:
```
{code}
```

Refactored Code:"""

            response = llm.invoke([HumanMessage(content=prompt)])
            return f"🔄 Refactored code ({refactor_type}):\n{response.content}"

        except Exception as e:
            return f"❌ Error refactoring code: {str(e)}"

    def get_project_structure(self, directory: str = ".") -> str:
        """Get comprehensive project structure"""
        try:
            structure = {}

            def build_tree(path, max_depth=3, current_depth=0):
                if current_depth >= max_depth:
                    return "..."

                items = {}
                try:
                    for item in sorted(os.listdir(path)):
                        if item.startswith('.'):
                            continue
                        item_path = os.path.join(path, item)
                        if os.path.isdir(item_path):
                            items[f"📁 {item}/"] = build_tree(item_path, max_depth, current_depth + 1)
                        else:
                            size = os.path.getsize(item_path)
                            items[f"📄 {item}"] = f"{size} bytes"
                except PermissionError:
                    items["❌ Permission Denied"] = ""
                return items

            structure = build_tree(os.path.abspath(directory))
            self.context.project_structure = structure

            return f"📂 Project Structure:\n{json.dumps(structure, indent=2)}"

        except Exception as e:
            return f"❌ Error getting project structure: {str(e)}"


    def run_tests(self, test_path: str = ".", test_type: str = "auto") -> str:
        """Run tests with auto-detection"""
        try:
            test_commands = {
                "python": ["python -m pytest", "python -m unittest discover"],
                "javascript": ["npm test", "yarn test", "jest"],
                "node": ["npm test", "mocha"],
                "auto": []
            }

            if test_type == "auto":
                # Auto-detect test framework
                if os.path.exists("package.json"):
                    test_commands["auto"] = test_commands["javascript"]
                elif any(f.endswith(".py") for f in os.listdir(".")):
                    test_commands["auto"] = test_commands["python"]
                else:
                    return "❌ Could not auto-detect test framework"

            commands = test_commands.get(test_type, test_commands["auto"])

            for cmd in commands:
                result = self.run_command(cmd)
                if "✅" in result:
                    return f"🧪 Tests executed:\n{result}"

            return "❌ No suitable test command found"

        except Exception as e:
            return f"❌ Error running tests: {str(e)}"

    def create_tools(self):
        """Create comprehensive LangChain tools from agent methods"""
        return [
            # Core System Tools
            Tool(
                name="run_command",
                description="Execute PowerShell/terminal commands with advanced error handling",
                func=lambda cmd: self.run_command(cmd)
            ),
            Tool(
                name="get_system_info",
                description="Get comprehensive system information",
                func=lambda: self.get_system_info()
            ),

            # File System Tools
            Tool(
                name="write_file",
                description="Write files with backup and validation. Format: path|content",
                func=lambda args: self.write_file(args.split("|")[0], "|".join(args.split("|")[1:]))
            ),
            Tool(
                name="read_file",
                description="Read files with line range support",
                func=lambda path: self.read_file(path)
            ),
            Tool(
                name="get_project_structure",
                description="Get comprehensive project structure",
                func=lambda dir: self.get_project_structure(dir if dir else ".")
            ),

            # Text & Code Manipulation Tools
            Tool(
                name="replace_string_in_file",
                description="Replace specific string or regex in file. Format: file_path|old_string|new_string|use_regex",
                func=lambda args: self.replace_string_in_file(*args.split("|")[:3], use_regex=args.split("|")[3].lower() == "true" if len(args.split("|")) > 3 else False)
            ),
            Tool(
                name="replace_in_multiple_files",
                description="Find & replace across directory. Format: pattern|replacement|directory|file_pattern|use_regex",
                func=lambda args: self.replace_in_multiple_files(*args.split("|")[:5])
            ),
            Tool(
                name="insert_text_at_position",
                description="Insert text at specific line. Format: file_path|text|line_number",
                func=lambda args: self.insert_text_at_position(*args.split("|"))
            ),
            Tool(
                name="insert_before_after",
                description="Insert text before/after matching line. Format: file_path|text|target_line|position",
                func=lambda args: self.insert_before_after(*args.split("|"))
            ),
            Tool(
                name="delete_lines_matching",
                description="Delete lines matching pattern. Format: file_path|pattern|use_regex",
                func=lambda args: self.delete_lines_matching(*args.split("|")[:2], use_regex=args.split("|")[2].lower() == "true" if len(args.split("|")) > 2 else True)
            ),
            Tool(
                name="delete_line_range",
                description="Delete line range. Format: file_path|start_line|end_line",
                func=lambda args: self.delete_line_range(args.split("|")[0], int(args.split("|")[1]), int(args.split("|")[2]))
            ),
            Tool(
                name="append_text_to_file",
                description="Add text to end of file. Format: file_path|text",
                func=lambda args: self.append_text_to_file(*args.split("|", 1))
            ),
            Tool(
                name="prepend_text_to_file",
                description="Add text to beginning of file. Format: file_path|text",
                func=lambda args: self.prepend_text_to_file(*args.split("|", 1))
            ),
            Tool(
                name="comment_out_matching_lines",
                description="Comment out matching lines. Format: file_path|pattern|comment_style",
                func=lambda args: self.comment_out_matching_lines(*args.split("|"))
            ),
            Tool(
                name="uncomment_lines",
                description="Uncomment lines. Format: file_path|comment_style",
                func=lambda args: self.uncomment_lines(*args.split("|"))
            ),
            Tool(
                name="extract_function",
                description="Extract code block into function. Format: file_path|function_pattern|new_function_name",
                func=lambda args: self.extract_function(*args.split("|"))
            ),

            # Search and Analysis Tools
            Tool(
                name="search_files",
                description="Search for files and content with pattern matching",
                func=lambda pattern: self.search_files(pattern)
            ),
            Tool(
                name="semantic_code_search",
                description="Search code using natural language. Format: query|directory",
                func=lambda args: self.semantic_code_search(*args.split("|"))
            ),
            Tool(
                name="grep_search",
                description="Advanced grep search with context. Format: pattern|directory",
                func=lambda args: self.grep_search(*args.split("|"))
            ),
            Tool(
                name="smart_text_replace",
                description="LLM-based meaning-aware replacement. Format: file_path|description",
                func=lambda args: self.smart_text_replace(*args.split("|", 1))
            ),
            Tool(
                name="highlight_code_block",
                description="Highlight matching code blocks. Format: file_path|pattern",
                func=lambda args: self.highlight_code_block(*args.split("|"))
            ),
            Tool(
                name="analyze_code",
                description="Analyze code structure and complexity",
                func=lambda code: self.analyze_code(code)
            ),

            # Testing and Debugging Tools
            Tool(
                name="test_search",
                description="Find tests related to source file",
                func=lambda source_file: self.test_search(source_file)
            ),
            Tool(
                name="run_tests",
                description="Run tests with auto-detection",
                func=lambda path: self.run_tests(path if path else ".")
            ),
            Tool(
                name="autonomous_debugger",
                description="AI-powered debugging. Format: error_message|code_context",
                func=lambda args: self.autonomous_debugger(*args.split("|", 1))
            ),
            Tool(
                name="lint_check",
                description="Run linting and static analysis. Format: file_path|language",
                func=lambda args: self.lint_check(*args.split("|"))
            ),
            Tool(
                name="self_repair",
                description="AI-suggested automatic code repairs",
                func=lambda file_path: self.self_repair(file_path)
            ),

            # AI-Powered Tools
            Tool(
                name="natural_language_to_code",
                description="Convert description to code. Format: description|language",
                func=lambda args: self.natural_language_to_code(*args.split("|"))
            ),
            Tool(
                name="intent_recognition",
                description="Analyze user intent and predict actions",
                func=lambda user_input: self.intent_recognition(user_input)
            ),
            Tool(
                name="generate_code",
                description="AI-powered code generation",
                func=lambda desc: self.generate_code(desc)
            ),
            Tool(
                name="refactor_code",
                description="AI-powered code refactoring",
                func=lambda code: self.refactor_code(code)
            ),
            Tool(
                name="fix_errors",
                description="Advanced error analysis and fixing suggestions",
                func=lambda error: self.fix_errors(error)
            ),

            # Web and Information Tools
            Tool(
                name="get_web_info",
                description="Retrieve information from web sources",
                func=lambda query: self.get_web_info(query)
            ),

            # Advanced Workflow Tools
            Tool(
                name="create_new_workspace",
                description="Setup complete development workspace. Format: project_name|project_type",
                func=lambda args: self.create_new_workspace(*args.split("|"))
            ),
            Tool(
                name="multi_step_loop",
                description="Execute multi-step task with iterative improvement. Format: task_description|max_iterations",
                func=lambda args: self.multi_step_loop(args.split("|")[0], int(args.split("|")[1]) if len(args.split("|")) > 1 else 5)
            ),
            Tool(
                name="code_duplication_removal",
                description="Detect and remove code duplication in directory",
                func=lambda directory: self.code_duplication_removal(directory if directory else ".")
            ),
            Tool(
                name="context_aware_refactor",
                description="Smart refactoring based on context. Format: file_path|refactor_goal",
                func=lambda args: self.context_aware_refactor(*args.split("|", 1))
            ),

            # Web and Network Tools
            Tool(
                name="fetch_webpage",
                description="Fetch and return webpage content",
                func=lambda url: self.fetch_webpage(url)
            ),
            Tool(
                name="install_python_packages",
                description="Install Python packages using pip. Format: package1,package2,package3",
                func=lambda packages: self.install_python_packages(packages)
            ),

            # Git and Version Control Tools
            Tool(
                name="get_changed_files",
                description="Get Git diff of modified files in directory",
                func=lambda directory: self.get_changed_files(directory if directory else ".")
            ),

            # Code Analysis Tools
            Tool(
                name="list_code_usages",
                description="Find where symbols are used. Format: symbol|directory",
                func=lambda args: self.list_code_usages(*args.split("|"))
            ),
            Tool(
                name="get_errors",
                description="Get lint, syntax, or compiler errors for file or project",
                func=lambda file_path: self.get_errors(file_path if file_path else None)
            ),

            # Additional File System Tools
            Tool(
                name="create_file",
                description="Create a file with content. Format: file_path|content",
                func=lambda args: self.create_file(*args.split("|", 1))
            ),
            Tool(
                name="create_directory",
                description="Create nested folders/directories",
                func=lambda directory_path: self.create_directory(directory_path)
            ),
            Tool(
                name="list_dir",
                description="List files/folders in a directory",
                func=lambda directory: self.list_dir(directory if directory else ".")
            ),
            Tool(
                name="file_search",
                description="Search files by glob patterns. Format: pattern|directory",
                func=lambda args: self.file_search(*args.split("|"))
            ),
            Tool(
                name="get_project_setup_info",
                description="Detect framework, language, tooling, etc.",
                func=lambda directory: self.get_project_setup_info(directory if directory else ".")
            ),

            # Additional Testing Tools
            Tool(
                name="test_failure",
                description="Capture and analyze test failure messages",
                func=lambda error_output: self.test_failure(error_output)
            ),
            Tool(
                name="code_linting_static_analysis",
                description="Combine all error & code checkers",
                func=lambda directory: self.code_linting_static_analysis(directory if directory else ".")
            ),

            # Advanced Code Manipulation Tools
            Tool(
                name="toggle_comments",
                description="Toggle comments on/off for matched lines. Format: file_path|pattern|comment_style",
                func=lambda args: self.toggle_comments(*args.split("|"))
            ),
            Tool(
                name="inline_function",
                description="Replace function call with full code logic inline. Format: file_path|function_name",
                func=lambda args: self.inline_function(*args.split("|"))
            ),
            Tool(
                name="rename_symbol_in_file",
                description="Rename variable/function/class inside a single file. Format: file_path|old_name|new_name",
                func=lambda args: self.rename_symbol_in_file(*args.split("|"))
            ),
            Tool(
                name="rename_symbol_project_wide",
                description="Rename symbol across project. Format: old_name|new_name|directory",
                func=lambda args: self.rename_symbol_project_wide(args.split("|")[0], args.split("|")[1], args.split("|")[2] if len(args.split("|")) > 2 else ".")
            ),
            Tool(
                name="move_block_to_new_file",
                description="Extract code block to new file. Format: source_file|target_file|block_pattern",
                func=lambda args: self.move_block_to_new_file(*args.split("|"))
            ),

            # Chunk-Level Editing Tools
            Tool(
                name="split_code_by_function",
                description="Break full code into logical function chunks",
                func=lambda file_path: self.split_code_by_function(file_path)
            ),
            Tool(
                name="extract_code_chunk",
                description="Extract specific region from start-end marker. Format: file_path|start_marker|end_marker",
                func=lambda args: self.extract_code_chunk(*args.split("|"))
            ),
            Tool(
                name="merge_code_chunks",
                description="Merge multiple blocks or files together. Format: file1|file2|output_file",
                func=lambda args: self.merge_code_chunks(*args.split("|"))
            ),
            Tool(
                name="diff_two_chunks",
                description="Compare differences between two code blocks. Format: chunk1|chunk2",
                func=lambda args: self.diff_two_chunks(*args.split("|", 1))
            ),

            # AI Reasoning Tools
            Tool(
                name="chain_of_thought_reasoning",
                description="Break down complex steps with reasoning",
                func=lambda problem: self.chain_of_thought_reasoning(problem)
            ),
            Tool(
                name="self_critique",
                description="Evaluate solutions and improve results. Format: solution|original_problem",
                func=lambda args: self.self_critique(*args.split("|", 1))
            ),

            # Enhanced Web and Browser Tools
            Tool(
                name="open_simple_browser",
                description="Open webpage in default browser",
                func=lambda url: self.open_simple_browser(url)
            ),
            Tool(
                name="github_repo_search",
                description="Search GitHub repositories",
                func=lambda query: self.github_repo_search(query)
            ),
            Tool(
                name="semantic_web_search",
                description="Natural language web search",
                func=lambda query: self.semantic_web_search(query)
            ),
            Tool(
                name="configure_python_environment",
                description="Setup and manage Python virtual environment. Format: env_name|python_version",
                func=lambda args: self.configure_python_environment(*args.split("|"))
            ),

            # Augment Agent Tools Integration
            Tool(
                name="augment_codebase_retrieval",
                description="Use Augment's codebase retrieval for semantic code search",
                func=lambda query: self.augment_codebase_retrieval(query)
            ),
            Tool(
                name="augment_web_search",
                description="Enhanced web search with Augment-style results",
                func=lambda query: self.augment_web_search(query)
            ),
            Tool(
                name="augment_code_generation",
                description="Advanced code generation with Augment intelligence. Format: description|language|style",
                func=lambda args: self.augment_code_generation(*args.split("|"))
            ),
            Tool(
                name="augment_diagnostics",
                description="Get comprehensive diagnostics like Augment's diagnostics tool",
                func=lambda file_paths: self.augment_diagnostics(file_paths.split(",") if file_paths else [])
            )
        ]

    def create_agent_prompt(self):
        """Create comprehensive system prompt for the agent with full tool context"""
        tools_list = self.create_tools()
        tool_descriptions = "\n".join([f"- {tool.name}: {tool.description}" for tool in tools_list])

        return f"""You are C CODE - an ELITE AUTONOMOUS CODING AGENT powered by Gemini AI with ADVANCED ORCHESTRATION capabilities.

🎯 MISSION: Achieve 10000% accuracy in all coding tasks with Claude-level intelligence and precision.

🧠 CORE INTELLIGENCE ARCHITECTURE:
- Advanced AI with natural language understanding and contextual awareness
- Multi-modal reasoning across code, documentation, and system architecture
- Predictive analysis and proactive problem-solving
- Self-monitoring and continuous improvement loops
- Deep semantic understanding of programming paradigms
- Cross-language expertise with best practices enforcement
- Real-time error detection and autonomous correction
- Intelligent code generation with optimization focus

🎯 ELITE CAPABILITIES:
- Full-stack development mastery (Python, JavaScript, TypeScript, React, Node.js, Go, Rust, etc.)
- Advanced system architecture and design patterns
- Database design and optimization
- DevOps and deployment automation
- Security analysis and vulnerability assessment
- Performance optimization and profiling
- Code review and quality assurance
- Documentation generation and maintenance
- Testing strategy and implementation
- Project management and workflow optimization

🧠 ADVANCED INTELLIGENCE FEATURES:
- Chain-of-thought reasoning with step-by-step analysis
- Self-critique and solution validation
- Context-aware decision making with memory persistence
- Predictive coding and intelligent autocomplete
- Semantic code search and cross-reference analysis
- Autonomous debugging with root cause analysis
- Background monitoring and proactive maintenance
- Intent recognition with multi-step planning
- Code quality assessment and improvement suggestions
- Performance bottleneck identification and resolution

🔄 SMART ORCHESTRATION WORKFLOW:
1. ANALYZE: Understand user input and detect intent
2. PLAN: Create intelligent execution plan with dependencies
3. ORCHESTRATE: Manage multiple tasks and background processes
4. EXECUTE: Perform actions with smart error handling
5. MONITOR: Track progress and system health
6. ADAPT: Adjust plan based on real-time feedback
7. OPTIMIZE: Learn and improve from each interaction

🛠️ COMPLETE TOOLSET ({len(tools_list)} tools available):

{tool_descriptions}

📊 TOOL CATEGORIES:
📁 File System: create_file, write_file, read_file, create_directory, list_dir, file_search, get_project_structure, get_project_setup_info
🧠 Text Manipulation: replace_string_in_file, replace_in_multiple_files, insert_text_at_position, insert_before_after, delete_lines_matching, delete_line_range, append_text_to_file, prepend_text_to_file, comment_out_matching_lines, uncomment_lines, extract_function
🔍 Search & Analysis: search_files, semantic_code_search, grep_search, smart_text_replace, highlight_code_block, analyze_code, list_code_usages
🧪 Testing & Debugging: test_search, run_tests, autonomous_debugger, lint_check, self_repair, test_failure, get_errors, code_linting_static_analysis
🤖 AI Intelligence: natural_language_to_code, intent_recognition, generate_code, refactor_code, fix_errors
🔧 Workflow: create_new_workspace, multi_step_loop, code_duplication_removal, context_aware_refactor
🌐 Web & Network: get_web_info, fetch_webpage, install_python_packages
💻 System & Git: run_command, get_system_info, get_changed_files

🎯 CLAUDE CODE BEHAVIOR PROTOCOL:
- ACCURACY FIRST: Every response must be precise, complete, and thoroughly tested
- INTELLIGENT ANALYSIS: Always analyze the full context before taking action
- PROACTIVE PROBLEM-SOLVING: Anticipate issues and provide preventive solutions
- COMPREHENSIVE EXECUTION: Complete tasks fully with attention to edge cases
- QUALITY ASSURANCE: Validate all code and solutions before delivery
- CONTINUOUS LEARNING: Adapt and improve based on user feedback and results
- EFFICIENT ORCHESTRATION: Optimize workflows and minimize redundant operations
- CLEAR COMMUNICATION: Provide detailed explanations with reasoning
- SECURITY CONSCIOUSNESS: Always consider security implications and best practices
- PERFORMANCE OPTIMIZATION: Ensure all solutions are efficient and scalable
- ERROR RESILIENCE: Implement robust error handling and recovery mechanisms
- DOCUMENTATION EXCELLENCE: Generate clear, comprehensive documentation
- TESTING THOROUGHNESS: Create comprehensive test suites for all code
- MAINTAINABILITY FOCUS: Write clean, readable, and maintainable code
- BEST PRACTICES ENFORCEMENT: Follow industry standards and conventions

🚀 ELITE AUTONOMOUS CAPABILITIES:
- INTELLIGENT PROJECT ANALYSIS: Deep understanding of project structure and requirements
- ADVANCED DEPENDENCY ORCHESTRATION: Smart package management with conflict resolution
- REAL-TIME SYSTEM MONITORING: Continuous performance and health assessment
- PREDICTIVE CODE INTELLIGENCE: Anticipate needs and suggest optimal solutions
- CONTEXT-AWARE REFACTORING: Intelligent code improvement with semantic understanding
- AUTONOMOUS ERROR RESOLUTION: Self-healing code with comprehensive error handling
- STRATEGIC TASK ORCHESTRATION: Optimal task sequencing and resource allocation
- PERFORMANCE OPTIMIZATION ENGINE: Continuous monitoring and improvement
- ADVANCED GIT INTEGRATION: Intelligent version control with conflict resolution
- ENVIRONMENT VIRTUALIZATION: Complete development environment management
- SECURITY VULNERABILITY SCANNING: Proactive security analysis and hardening
- CODE QUALITY ENFORCEMENT: Automated code review and improvement suggestions
- DOCUMENTATION AUTOMATION: Intelligent documentation generation and maintenance
- TESTING AUTOMATION: Comprehensive test suite generation and execution
- DEPLOYMENT ORCHESTRATION: Automated CI/CD pipeline management

🎯 CLAUDE CODE EXECUTION PROTOCOL:
1. DEEP ANALYSIS: Thoroughly understand the request and context
2. STRATEGIC PLANNING: Create comprehensive execution strategy
3. QUALITY VALIDATION: Verify approach before implementation
4. PRECISE EXECUTION: Implement with attention to detail and edge cases
5. COMPREHENSIVE TESTING: Validate all functionality thoroughly
6. PERFORMANCE OPTIMIZATION: Ensure efficiency and scalability
7. DOCUMENTATION: Provide clear explanations and documentation
8. CONTINUOUS MONITORING: Track results and optimize continuously

RESPONSE GUIDELINES:
- Use natural, conversational language with technical precision
- Provide detailed reasoning for all decisions and actions
- Demonstrate deep understanding of coding principles and best practices
- Use fast typewriter effects for engaging, GPTme-style responses
- Leverage all {len(tools_list)} tools strategically for optimal solutions
- Always aim for 10000% accuracy and completeness
- Be proactive in identifying and solving potential issues
- Maintain Claude-level intelligence and problem-solving capability"""

    def run_agent(self):
        """Main agent execution loop with Claude Code style interface"""
        # Initialize Claude Code style interface
        self.tui.print_header(
            "claude",
            "Ready to help with your coding tasks"
        )

        # Start background monitoring silently
        self.start_background_monitor()

        # Show current directory like Claude Code
        print(f"{self.tui.theme.FG_DARK_GRAY}Working directory: {os.path.basename(self.context.current_directory)}{self.tui.theme.RESET}")
        print(f"{self.tui.theme.FG_DARK_GRAY}Tools available: {len(self.create_tools())}{self.tui.theme.RESET}")
        print()

        # Create agent with tools
        tools = self.create_tools()

        # Create prompt template for ReAct agent
        prompt_template = """You are an ADVANCED AUTONOMOUS CLI CODING AGENT powered by Gemini AI.

🎯 CORE CAPABILITIES:
- Full-stack development (Python, JavaScript, TypeScript, React, Node.js, etc.)
- Advanced file operations and project management
- Terminal command execution with PowerShell support
- Code analysis, generation, and refactoring
- Error detection and autonomous fixing
- Web information retrieval
- Multi-threaded task execution
- Context-aware decision making

TOOLS:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""

        prompt = PromptTemplate(
            input_variables=["input", "agent_scratchpad", "tools", "tool_names"],
            template=prompt_template
        )

        # Create agent
        agent = create_react_agent(llm, tools, prompt)
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=10
        )

        while True:
            try:
                # Get Claude Code style input
                user_input = self.tui.get_styled_input().strip()

                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print(f"{self.tui.theme.FG_GRAY}Goodbye!{self.tui.theme.RESET}")
                    break

                if user_input.lower() in ['help', '/help']:
                    self.show_help_claude_style()
                    continue

                if user_input.lower() in ['status', '/status']:
                    self.show_status_claude_style()
                    continue

                if user_input.lower() in ['clear', '/clear']:
                    self.tui.print_header("claude", "Session cleared")
                    continue

                if not user_input:
                    continue

                # Smart orchestration with interactive feedback
                print()
                self.tui.animate_loading("Analyzing request with AI intelligence", 1.5)
                intent_analysis = self.orchestrator.analyze_intent(user_input)

                # Show intent analysis in styled box
                intent_content = f"""Primary Intent: {intent_analysis['primary_intent']}
Detected Intents: {', '.join(intent_analysis['all_intents'])}
Complexity Score: {intent_analysis['complexity']}
Requires Planning: {intent_analysis['requires_planning']}"""

                self.tui.print_box(intent_content, "🧠 Intent Analysis", "info")

                if intent_analysis["requires_planning"]:
                    self.tui.animate_loading("Creating intelligent execution plan", 1.0)
                    execution_plan = self.orchestrator.create_execution_plan(intent_analysis, user_input)
                    if execution_plan:
                        plan_content = f"Created execution plan with {len(execution_plan)} tasks:\n\n"
                        for i, task in enumerate(execution_plan, 1):
                            plan_content += f"{i}. {task.description}\n"

                        self.tui.print_box(plan_content.strip(), "📋 Execution Plan", "accent")
                        self.context.task_queue.extend(execution_plan)

                # Add comprehensive context to user input
                context_info = f"""
🔍 CURRENT CONTEXT:
- Directory: {self.context.current_directory}
- Active Files: {", ".join(self.context.active_files[-5:]) if self.context.active_files else "None"}
- Last Command: {self.context.command_history[-1] if self.context.command_history else "None"}
- Session Stats: {self.context.session_stats['commands_executed']} commands, {self.context.session_stats['files_modified']} files modified
- Intent Analysis: {intent_analysis}
- Task Queue: {len(self.context.task_queue)} pending tasks

🎯 USER REQUEST: {user_input}

🧠 SMART ORCHESTRATION INSTRUCTIONS:
- Use intent analysis to guide your approach
- Consider the execution plan if available
- Leverage all available tools for comprehensive solutions
- Provide proactive suggestions and optimizations
- Monitor for errors and apply auto-fixes
- Update session statistics as you work"""

                # Execute with Claude Code style
                print()
                self.tui.print_claude_thinking("Thinking...")

                # Run agent with comprehensive error handling
                try:
                    result = agent_executor.invoke({
                        "input": context_info
                    })

                    if result.get("output"):
                        # Show Claude Code style response
                        self.tui.print_claude_response_start()
                        self.tui.stream_response(result['output'])
                        print(f"{self.tui.theme.RESET}")

                        # Update session statistics
                        self.context.session_stats['commands_executed'] += 1

                        # Check if any tasks were completed
                        completed_tasks = [task for task in self.context.task_queue if task.status == "completed"]
                        if completed_tasks:
                            task_content = f"Completed {len(completed_tasks)} planned tasks from execution queue"
                            self.tui.print_box(task_content, "🎯 Task Progress", "info")

                except Exception as e:
                    # Show error with typewriter effect
                    print(f"\n{self.tui.theme.RED}❌ Execution Error:{self.tui.theme.RESET}")
                    self.tui.stream_response(f"Agent execution error occurred:\n\n{str(e)}", delay=0.02)

                    # Smart error recovery with animation
                    self.tui.animate_loading("Initiating smart error recovery", 1.5)

                    # Try autonomous debugging with typewriter effect
                    debug_result = self.autonomous_debugger(str(e), user_input)
                    print(f"\n{self.tui.theme.YELLOW}🐛 Debug Analysis:{self.tui.theme.RESET}")
                    self.tui.stream_response(debug_result, delay=0.015)

                    # Try to fix the error automatically with typewriter effect
                    fix_suggestion = self.fix_errors(str(e), user_input)
                    print(f"\n{self.tui.theme.CYAN}🔧 Auto-Fix Suggestions:{self.tui.theme.RESET}")
                    self.tui.stream_response(fix_suggestion, delay=0.015)

                    # Update error statistics
                    self.context.last_error = str(e)
                    self.context.session_stats['errors_fixed'] = self.context.session_stats.get('errors_fixed', 0) + 1

            except KeyboardInterrupt:
                print("\n⏸️ Interrupted. Type 'exit' to quit or continue with new command.")
                continue
            except Exception as e:
                error_content = f"Unexpected error occurred:\n\n{str(e)}"
                self.tui.print_box(error_content, "❌ System Error", "error")
                continue

    def show_help_interactive(self):
        """Show interactive help with Tokyo Night styling"""
        self.tui.clear_screen()
        self.tui.print_header("Advanced CLI Coding Agent v3.0", "📚 Comprehensive Help & Documentation")

        # Core capabilities
        capabilities_content = """• Build complete full-stack applications with smart orchestration
• Analyze and fix code errors with autonomous debugging
• Generate code from natural language with AI intelligence
• Refactor and optimize code with semantic understanding
• Advanced file operations with backup and validation
• Execute terminal commands with smart error handling
• Comprehensive testing and quality assurance
• Web information retrieval and research"""

        self.tui.print_box(capabilities_content, "🎯 Core Capabilities", "accent")

        # Smart orchestration features
        orchestration_content = """• Intent recognition and predictive planning
• Multi-task execution with dependency management
• Background monitoring and proactive fixes
• Context-aware decision making
• Resource optimization and performance tracking
• Automatic error recovery and self-repair"""

        self.tui.print_box(orchestration_content, "🧠 Smart Orchestration Features", "info")

        # Example commands
        examples_content = """• "Create a full-stack React app with authentication and database"
• "Debug and fix all errors in my Python project automatically"
• "Refactor my entire codebase for better performance and maintainability"
• "Generate comprehensive tests for all my modules with coverage analysis"
• "Analyze my project structure and suggest improvements"
• "Convert this natural language specification into working code"
• "Find and fix all security vulnerabilities in my application"
• "Optimize my code for production deployment"""

        self.tui.print_box(examples_content, "💡 Example Commands", "success")

        # Special commands
        special_content = """• help - Show this comprehensive help
• status - Show detailed context, statistics, and active tasks
• clear - Clear screen and reset display
• exit/quit - Exit the agent gracefully"""

        self.tui.print_box(special_content, "🔧 Special Commands", "warning")

        # Status bar
        self.tui.print_status_bar("Press any key to continue...", f"Tools: {len(self.create_tools())}")
        input()

    def show_help_claude_style(self):
        """Show Claude Code style help"""
        print(f"{self.tui.theme.FG_WHITE}Claude Code - Elite Coding Agent{self.tui.theme.RESET}")
        print()
        print(f"{self.tui.theme.FG_GRAY}Available commands:{self.tui.theme.RESET}")
        print(f"  {self.tui.theme.FG_WHITE}/help{self.tui.theme.RESET}     Show this help")
        print(f"  {self.tui.theme.FG_WHITE}/status{self.tui.theme.RESET}   Show session status")
        print(f"  {self.tui.theme.FG_WHITE}/clear{self.tui.theme.RESET}    Clear the screen")
        print(f"  {self.tui.theme.FG_WHITE}exit{self.tui.theme.RESET}      Exit Claude Code")
        print()
        print(f"{self.tui.theme.FG_GRAY}Examples:{self.tui.theme.RESET}")
        print(f"  {self.tui.theme.FG_WHITE}Create a React app with TypeScript{self.tui.theme.RESET}")
        print(f"  {self.tui.theme.FG_WHITE}Fix the bug in main.py{self.tui.theme.RESET}")
        print(f"  {self.tui.theme.FG_WHITE}Refactor this code for better performance{self.tui.theme.RESET}")
        print(f"  {self.tui.theme.FG_WHITE}Generate tests for my API endpoints{self.tui.theme.RESET}")
        print()

    def show_status_claude_style(self):
        """Show Claude Code style status"""
        uptime = datetime.now() - self.context.session_stats['start_time']

        print(f"{self.tui.theme.FG_WHITE}Session Status{self.tui.theme.RESET}")
        print()
        print(f"{self.tui.theme.FG_GRAY}Directory:{self.tui.theme.RESET} {self.context.current_directory}")
        print(f"{self.tui.theme.FG_GRAY}Uptime:{self.tui.theme.RESET} {str(uptime).split('.')[0]}")
        print(f"{self.tui.theme.FG_GRAY}Commands executed:{self.tui.theme.RESET} {self.context.session_stats.get('commands_executed', 0)}")
        print(f"{self.tui.theme.FG_GRAY}Files modified:{self.tui.theme.RESET} {self.context.session_stats.get('files_modified', 0)}")
        print(f"{self.tui.theme.FG_GRAY}Tools available:{self.tui.theme.RESET} {len(self.create_tools())}")
        print()

    def show_help(self):
        """Show comprehensive help information"""
        help_text = """
🤖 ADVANCED CLI CODING AGENT v3.0 - COMPREHENSIVE HELP

🎯 CORE CAPABILITIES:
• Build complete full-stack applications with smart orchestration
• Analyze and fix code errors with autonomous debugging
• Generate code from natural language with AI intelligence
• Refactor and optimize code with semantic understanding
• Advanced file operations with backup and validation
• Execute terminal commands with smart error handling
• Comprehensive testing and quality assurance
• Web information retrieval and research

🧠 SMART ORCHESTRATION FEATURES:
• Intent recognition and predictive planning
• Multi-task execution with dependency management
• Background monitoring and proactive fixes
• Context-aware decision making
• Resource optimization and performance tracking
• Automatic error recovery and self-repair

�️ COMPREHENSIVE TOOLSET:

📁 File System Operations:
• Advanced file reading/writing with backup
• Project structure analysis and organization
• Batch file operations across directories

🧠 Text & Code Manipulation:
• Smart find & replace with regex support
• Precise text insertion and deletion
• Comment management and code formatting
• Function extraction and modularization
• Multi-file refactoring operations

🔍 Search & Analysis:
• Semantic code search with natural language
• Advanced grep with context and highlighting
• Code structure analysis and complexity metrics
• Pattern-based file and content discovery

🧪 Testing & Quality Assurance:
• Automatic test discovery and execution
• Comprehensive linting and static analysis
• AI-powered debugging and error resolution
• Code quality assessment and improvement

🤖 AI-Powered Intelligence:
• Natural language to code conversion
• Intent recognition and action prediction
• Intelligent code generation and refactoring
• Context-aware suggestions and optimizations

💡 EXAMPLE COMMANDS:
• "Create a full-stack React app with authentication and database"
• "Debug and fix all errors in my Python project automatically"
• "Refactor my entire codebase for better performance and maintainability"
• "Generate comprehensive tests for all my modules with coverage analysis"
• "Analyze my project structure and suggest improvements"
• "Convert this natural language specification into working code"
• "Find and fix all security vulnerabilities in my application"
• "Optimize my code for production deployment"

🔧 SPECIAL COMMANDS:
• help - Show this comprehensive help
• status - Show detailed context, statistics, and active tasks
• exit/quit - Exit the agent gracefully

🚀 AUTONOMOUS FEATURES:
• Smart project type detection and setup
• Intelligent dependency management
• Background system monitoring and optimization
• Predictive code suggestions and completions
• Context-aware refactoring recommendations
• Automatic error detection and resolution
• Task queue management and prioritization
• Learning from interaction patterns for improvement
"""
        print(help_text)

    def show_status_interactive(self):
        """Show interactive status with Tokyo Night styling"""
        self.tui.clear_screen()
        self.tui.print_header("Advanced CLI Coding Agent v3.0", "📊 Comprehensive System Status")

        uptime = datetime.now() - self.context.session_stats['start_time']

        # Environment info
        env_content = f"""Current Directory: {self.context.current_directory}
Python Version: {platform.python_version()}
Platform: {platform.system()} {platform.release()}
Session Uptime: {str(uptime).split('.')[0]}"""

        self.tui.print_box(env_content, "🏠 Environment", "info")

        # Session statistics
        stats_content = f"""Commands Executed: {self.context.session_stats.get('commands_executed', 0)}
Files Modified: {self.context.session_stats.get('files_modified', 0)}
Errors Fixed: {self.context.session_stats.get('errors_fixed', 0)}
Tests Run: {self.context.session_stats.get('tests_run', 0)}
Active Files: {len(self.context.active_files)}
Memory Buffer: {len(self.memory.buffer)} messages"""

        self.tui.print_box(stats_content, "📈 Session Statistics", "success")

        # Task management
        task_content = f"""Total Tasks: {len(self.context.task_queue)}
Pending: {len([t for t in self.context.task_queue if t.status == 'pending'])}
In Progress: {len([t for t in self.context.task_queue if t.status == 'in_progress'])}
Completed: {len([t for t in self.context.task_queue if t.status == 'completed'])}
Failed: {len([t for t in self.context.task_queue if t.status == 'failed'])}"""

        self.tui.print_box(task_content, "🎯 Task Management", "accent")

        # System resources
        try:
            resource_content = f"""CPU Usage: {psutil.cpu_percent()}%
Memory Usage: {psutil.virtual_memory().percent}%
Disk Usage: {psutil.disk_usage('.').percent}%
Background Monitor: {'Active' if self.background_monitor and self.background_monitor.is_alive() else 'Inactive'}"""

            self.tui.print_box(resource_content, "💾 System Resources", "warning")
        except:
            self.tui.print_box("Resource monitoring unavailable", "💾 System Resources", "error")

        # Recent files (if any)
        if self.context.active_files:
            files_content = "\n".join([f"• {f}" for f in self.context.active_files[-5:]])
            self.tui.print_box(files_content, f"📁 Recent Files ({len(self.context.active_files)} total)", "info")

        # Status bar
        self.tui.print_status_bar("Press any key to continue...", f"Last Error: {self.context.last_error or 'None'}")
        input()

    def show_status(self):
        """Show comprehensive agent status"""
        uptime = datetime.now() - self.context.session_stats['start_time']

        print(f"""
📊 ADVANCED AGENT STATUS v3.0

🏠 ENVIRONMENT:
• Current Directory: {self.context.current_directory}
• Python Version: {platform.python_version()}
• Platform: {platform.system()} {platform.release()}
• Session Uptime: {str(uptime).split('.')[0]}

📈 SESSION STATISTICS:
• Commands Executed: {self.context.session_stats.get('commands_executed', 0)}
• Files Modified: {self.context.session_stats.get('files_modified', 0)}
• Errors Fixed: {self.context.session_stats.get('errors_fixed', 0)}
• Tests Run: {self.context.session_stats.get('tests_run', 0)}
• Active Files: {len(self.context.active_files)}
• Command History: {len(self.context.command_history)} commands
• Memory Buffer: {len(self.memory.buffer)} messages
• Cache Size: {len(self.cache)} items

🎯 TASK MANAGEMENT:
• Task Queue: {len(self.context.task_queue)} tasks
• Pending Tasks: {len([t for t in self.context.task_queue if t.status == 'pending'])}
• In Progress: {len([t for t in self.context.task_queue if t.status == 'in_progress'])}
• Completed: {len([t for t in self.context.task_queue if t.status == 'completed'])}
• Failed: {len([t for t in self.context.task_queue if t.status == 'failed'])}

🧠 INTELLIGENCE STATUS:
• Intent History: {len(self.context.intent_history)} analyzed
• Code Chunks: {len(self.context.code_chunks)} tracked
• Background Monitor: {'Active' if self.background_monitor and self.background_monitor.is_alive() else 'Inactive'}
• Last Error: {self.context.last_error or 'None'}

📁 RECENT FILES ({len(self.context.active_files[-10:])} of {len(self.context.active_files)}):
{chr(10).join([f"  • {f}" for f in self.context.active_files[-10:]]) if self.context.active_files else "  None"}

⚡ RECENT COMMANDS ({len(self.context.command_history[-5:])} of {len(self.context.command_history)}):
{chr(10).join([f"  • {cmd}" for cmd in self.context.command_history[-5:]]) if self.context.command_history else "  None"}

🔄 ACTIVE TASKS:
{chr(10).join([f"  • {task.description} ({task.status})" for task in self.context.task_queue[-5:] if task.status in ['pending', 'in_progress']]) if self.context.task_queue else "  None"}

💾 SYSTEM RESOURCES:
• CPU Usage: {psutil.cpu_percent()}%
• Memory Usage: {psutil.virtual_memory().percent}%
• Disk Usage: {psutil.disk_usage('.').percent}%
• Running Processes: {len(self.running_processes)}
""")

# Initialize and run the agent
if __name__ == "__main__":
    agent = AdvancedCodingAgent()
    agent.run_agent()